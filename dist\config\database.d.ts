import { Pool } from 'pg';
export declare const query: (text: string, params?: any[]) => Promise<import("pg").QueryResult<any>>;
export declare const getClient: () => Promise<import("pg").PoolClient>;
export declare const initDatabase: () => Promise<void>;
declare const _default: {
    query: (text: string, params?: any[]) => Promise<import("pg").QueryResult<any>>;
    getClient: () => Promise<import("pg").PoolClient>;
    initDatabase: () => Promise<void>;
    pool: Pool;
};
export default _default;
//# sourceMappingURL=database.d.ts.map