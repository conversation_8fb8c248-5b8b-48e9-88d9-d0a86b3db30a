"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const app_1 = require("./app");
const startServer = async () => {
    try {
        const app = new app_1.App();
        await app.initialize();
        app.listen();
    }
    catch (error) {
        console.error('❌ Failed to start server:', error);
        process.exit(1);
    }
};
startServer();
//# sourceMappingURL=server.js.map