"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.WhatsAppMessageParser = void 0;
const errorHandler_1 = require("../middleware/errorHandler");
class WhatsAppMessageParser {
    static parseWebhookPayload(payload) {
        try {
            if (!payload || typeof payload !== 'object') {
                throw new errorHandler_1.ValidationError('Invalid webhook payload: payload must be an object');
            }
            if (payload.object !== 'whatsapp_business_account') {
                throw new errorHandler_1.ValidationError('Invalid webhook payload: object must be whatsapp_business_account');
            }
            if (!Array.isArray(payload.entry)) {
                throw new errorHandler_1.ValidationError('Invalid webhook payload: entry must be an array');
            }
            const messages = [];
            const userProfiles = [];
            for (const entry of payload.entry) {
                if (!Array.isArray(entry.changes)) {
                    continue;
                }
                for (const change of entry.changes) {
                    if (change.field !== 'messages') {
                        continue;
                    }
                    const value = change.value;
                    if (!value || !value.metadata) {
                        continue;
                    }
                    const metadata = value.metadata;
                    if (value.contacts && Array.isArray(value.contacts)) {
                        for (const contact of value.contacts) {
                            userProfiles.push({
                                phoneNumber: contact.wa_id,
                                name: contact.profile?.name,
                                displayPhoneNumber: metadata.display_phone_number,
                                phoneNumberId: metadata.phone_number_id
                            });
                        }
                    }
                    if (value.messages && Array.isArray(value.messages)) {
                        for (const message of value.messages) {
                            const parsedMessage = this.parseMessage(message, metadata);
                            if (parsedMessage) {
                                messages.push(parsedMessage);
                            }
                        }
                    }
                }
            }
            errorHandler_1.logger.info('Parsed WhatsApp webhook payload', {
                messageCount: messages.length,
                userProfileCount: userProfiles.length
            });
            return { messages, userProfiles };
        }
        catch (error) {
            errorHandler_1.logger.error('Error parsing WhatsApp webhook payload', error instanceof Error ? error : new Error(String(error)));
            throw error;
        }
    }
    static parseMessage(message, metadata) {
        try {
            if (!message.id || !message.from || !message.timestamp || !message.type) {
                errorHandler_1.logger.warn('Invalid message structure, skipping', { message });
                return null;
            }
            const baseMessage = {
                messageId: message.id,
                from: message.from,
                to: metadata.phone_number_id,
                timestamp: message.timestamp,
                type: this.normalizeMessageType(message.type),
                context: message.context
            };
            switch (message.type) {
                case 'text':
                    if (message.text && message.text.body) {
                        baseMessage.text = message.text.body;
                    }
                    break;
                case 'image':
                    if (message.image) {
                        baseMessage.image = {
                            id: message.image.id,
                            mime_type: message.image.mime_type,
                            sha256: message.image.sha256,
                            caption: message.image.caption
                        };
                    }
                    break;
                case 'location':
                    if (message.location) {
                        baseMessage.location = {
                            latitude: message.location.latitude,
                            longitude: message.location.longitude,
                            name: message.location.name,
                            address: message.location.address
                        };
                    }
                    break;
                case 'contacts':
                    if (message.contacts && Array.isArray(message.contacts) && message.contacts.length > 0) {
                        const contact = message.contacts[0];
                        if (contact.name && contact.phones && contact.phones.length > 0) {
                            baseMessage.contact = {
                                name: contact.name.formatted_name,
                                phone: contact.phones[0].phone
                            };
                        }
                    }
                    break;
                default:
                    errorHandler_1.logger.info('Unsupported message type', { type: message.type });
                    baseMessage.type = 'unknown';
            }
            return baseMessage;
        }
        catch (error) {
            errorHandler_1.logger.error('Error parsing individual message', error instanceof Error ? error : new Error(String(error)), { message });
            return null;
        }
    }
    static normalizeMessageType(type) {
        const supportedTypes = [
            'text', 'image', 'audio', 'video', 'document', 'location', 'contacts'
        ];
        if (supportedTypes.includes(type)) {
            return type;
        }
        return 'unknown';
    }
    static extractUserQuery(message) {
        switch (message.type) {
            case 'text':
                return message.text?.trim() || null;
            case 'location':
                if (message.location) {
                    const { latitude, longitude, name, address } = message.location;
                    return `I'm at location: ${name || address || `${latitude}, ${longitude}`}. What businesses are nearby?`;
                }
                return null;
            case 'image':
                return message.image?.caption?.trim() || 'I sent you an image. Can you help me find related businesses?';
            default:
                return null;
        }
    }
    static generateSessionId(phoneNumber) {
        const cleanNumber = phoneNumber.replace(/\D/g, '');
        return `whatsapp_${cleanNumber}`;
    }
    static extractUserPreferences(message, userProfile) {
        const preferences = {};
        if (message.type === 'location' && message.location) {
            preferences.location = message.location.name || message.location.address;
        }
        if (message.type === 'text' && message.text) {
            const text = message.text.toLowerCase();
            const locationKeywords = ['dubai', 'abu dhabi', 'sharjah', 'ajman', 'fujairah', 'ras al khaimah', 'umm al quwain'];
            for (const location of locationKeywords) {
                if (text.includes(location)) {
                    preferences.location = location;
                    break;
                }
            }
        }
        return preferences;
    }
    static validateWebhookSignature(payload, signature, secret) {
        try {
            const crypto = require('crypto');
            const expectedSignature = crypto
                .createHmac('sha256', secret)
                .update(payload)
                .digest('hex');
            return crypto.timingSafeEqual(Buffer.from(signature), Buffer.from(`sha256=${expectedSignature}`));
        }
        catch (error) {
            errorHandler_1.logger.error('Error validating webhook signature', error instanceof Error ? error : new Error(String(error)));
            return false;
        }
    }
}
exports.WhatsAppMessageParser = WhatsAppMessageParser;
//# sourceMappingURL=WhatsAppMessageParser.js.map