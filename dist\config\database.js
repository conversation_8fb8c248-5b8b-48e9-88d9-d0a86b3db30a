"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.initDatabase = exports.getClient = exports.query = void 0;
const pg_1 = require("pg");
const environment_1 = require("./environment");
const dbConfig = environment_1.config.database.url ? {
    connectionString: environment_1.config.database.url,
    ssl: {
        rejectUnauthorized: false
    },
    max: 20,
    idleTimeoutMillis: 30000,
    connectionTimeoutMillis: 10000,
} : {
    host: environment_1.config.database.host,
    port: environment_1.config.database.port,
    database: environment_1.config.database.name,
    user: environment_1.config.database.username,
    password: environment_1.config.database.password,
    ssl: {
        rejectUnauthorized: false
    },
    max: 20,
    idleTimeoutMillis: 30000,
    connectionTimeoutMillis: 10000,
};
const pool = new pg_1.Pool(dbConfig);
pool.on('connect', () => {
    console.log('Connected to PostgreSQL database');
});
pool.on('error', (err) => {
    console.error('Unexpected error on idle PostgreSQL client', err);
    process.exit(-1);
});
const query = async (text, params) => {
    const start = Date.now();
    try {
        const res = await pool.query(text, params);
        const duration = Date.now() - start;
        console.log('Executed query', { text, duration, rows: res.rowCount });
        return res;
    }
    catch (error) {
        console.error('Error executing query', { text, error });
        throw error;
    }
};
exports.query = query;
const getClient = async () => {
    return await pool.connect();
};
exports.getClient = getClient;
const initDatabase = async () => {
    try {
        const client = await Promise.race([
            pool.connect(),
            new Promise((_, reject) => setTimeout(() => reject(new Error('Database connection timeout')), 10000))
        ]);
        console.log('✅ Successfully connected to PostgreSQL database');
        client.release();
    }
    catch (error) {
        console.warn('⚠️  Database connection failed, but server will continue without database:', error);
    }
};
exports.initDatabase = initDatabase;
exports.default = {
    query: exports.query,
    getClient: exports.getClient,
    initDatabase: exports.initDatabase,
    pool,
};
//# sourceMappingURL=database.js.map