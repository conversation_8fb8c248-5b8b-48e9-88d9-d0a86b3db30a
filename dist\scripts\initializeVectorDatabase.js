"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.VectorDatabaseInitializer = void 0;
const database_1 = require("../config/database");
const VectorSearchService_1 = require("../services/VectorSearchService");
const environment_1 = require("../config/environment");
class VectorDatabaseInitializer {
    static async initialize() {
        console.log('🚀 Starting vector database initialization...');
        try {
            await this.enablePgVectorExtension();
            await this.addEmbeddingColumns();
            await this.createVectorIndexes();
            console.log('✅ Vector database initialization completed successfully!');
            console.log('📝 Next steps:');
            console.log('   1. Run generateEmbeddingsForExistingData() to create embeddings for existing business data');
            console.log('   2. Set up a cron job to update embeddings when data changes');
            console.log('   3. Monitor vector search performance and adjust similarity thresholds as needed');
        }
        catch (error) {
            console.error('❌ Vector database initialization failed:', error);
            throw error;
        }
    }
    static async enablePgVectorExtension() {
        console.log('📦 Enabling pgvector extension...');
        try {
            await (0, database_1.query)('CREATE EXTENSION IF NOT EXISTS vector;');
            console.log('✅ pgvector extension enabled successfully');
        }
        catch (error) {
            console.error('❌ Failed to enable pgvector extension:', error);
            throw error;
        }
    }
    static async addEmbeddingColumns() {
        console.log('🗃️  Adding embedding columns to tables...');
        const tables = [
            'Restaurants',
            'Branches',
            'Shops',
            'ShopBranches',
            'Clubs',
            'Items',
            'ShopItems',
            'Categories',
            'ShopCategories',
            'Facilities'
        ];
        const embeddingDimension = environment_1.config.vectorSearch.embeddingDimensions;
        for (const table of tables) {
            try {
                const columnExists = await (0, database_1.query)(`
          SELECT column_name 
          FROM information_schema.columns 
          WHERE table_name = $1 AND column_name = 'embedding'
        `, [table]);
                if (columnExists.rows.length === 0) {
                    await (0, database_1.query)(`ALTER TABLE "${table}" ADD COLUMN embedding vector(${embeddingDimension});`);
                    console.log(`✅ Added embedding column to ${table}`);
                }
                else {
                    console.log(`ℹ️  Embedding column already exists in ${table}`);
                }
            }
            catch (error) {
                console.error(`❌ Failed to add embedding column to ${table}:`, error);
            }
        }
    }
    static async createVectorIndexes() {
        console.log('🔍 Creating vector indexes...');
        const indexConfigurations = [
            { table: 'Restaurants', indexName: 'restaurants_embedding_idx' },
            { table: 'Branches', indexName: 'branches_embedding_idx' },
            { table: 'Shops', indexName: 'shops_embedding_idx' },
            { table: 'ShopBranches', indexName: 'shop_branches_embedding_idx' },
            { table: 'Clubs', indexName: 'clubs_embedding_idx' },
            { table: 'Items', indexName: 'items_embedding_idx' },
            { table: 'ShopItems', indexName: 'shop_items_embedding_idx' },
            { table: 'Categories', indexName: 'categories_embedding_idx' },
            { table: 'ShopCategories', indexName: 'shop_categories_embedding_idx' },
            { table: 'Facilities', indexName: 'facilities_embedding_idx' }
        ];
        for (const { table, indexName } of indexConfigurations) {
            try {
                const indexExists = await (0, database_1.query)(`
          SELECT indexname 
          FROM pg_indexes 
          WHERE tablename = $1 AND indexname = $2
        `, [table, indexName]);
                if (indexExists.rows.length === 0) {
                    await (0, database_1.query)(`
            CREATE INDEX ${indexName} ON "${table}" 
            USING hnsw (embedding vector_cosine_ops)
            WITH (m = 16, ef_construction = 64);
          `);
                    console.log(`✅ Created vector index ${indexName} on ${table}`);
                }
                else {
                    console.log(`ℹ️  Vector index ${indexName} already exists on ${table}`);
                }
            }
            catch (error) {
                console.error(`❌ Failed to create vector index on ${table}:`, error);
            }
        }
    }
    static async generateEmbeddingsForExistingData() {
        console.log('🧠 Generating embeddings for existing business data...');
        console.log('⚠️  This process may take a while depending on the amount of data...');
        try {
            await this.generateEmbeddingsForTable('Restaurants', 'restaurant');
            await this.generateEmbeddingsForTable('Branches', 'branch');
            await this.generateEmbeddingsForTable('Shops', 'shop');
            await this.generateEmbeddingsForTable('ShopBranches', 'shop_branch');
            await this.generateEmbeddingsForTable('Clubs', 'club');
            await this.generateEmbeddingsForTable('Items', 'food_item');
            await this.generateEmbeddingsForTable('ShopItems', 'shop_item');
            await this.generateEmbeddingsForTable('Facilities', 'facility');
            console.log('✅ Embedding generation completed for all tables!');
        }
        catch (error) {
            console.error('❌ Failed to generate embeddings:', error);
            throw error;
        }
    }
    static async generateEmbeddingsForTable(tableName, entityType) {
        console.log(`📊 Processing ${tableName}...`);
        try {
            const result = await (0, database_1.query)(`
        SELECT * FROM "${tableName}" 
        WHERE embedding IS NULL 
        AND status = true
        ORDER BY created_at DESC
        LIMIT 100
      `);
            if (result.rows.length === 0) {
                console.log(`ℹ️  No records to process in ${tableName}`);
                return;
            }
            console.log(`📝 Found ${result.rows.length} records to process in ${tableName}`);
            const batchSize = 5;
            for (let i = 0; i < result.rows.length; i += batchSize) {
                const batch = result.rows.slice(i, i + batchSize);
                for (const record of batch) {
                    try {
                        const embedding = await VectorSearchService_1.vectorSearchService.createBusinessEmbedding(record, entityType);
                        const idColumn = this.getIdColumn(tableName);
                        await (0, database_1.query)(`
              UPDATE "${tableName}" 
              SET embedding = $1::vector 
              WHERE ${idColumn} = $2
            `, [JSON.stringify(embedding), record[idColumn]]);
                        console.log(`✅ Generated embedding for ${record.name || record.restaurant_name || record.shop_name || record.club_name || record.item_name || record.facility_name || record.category_name}`);
                    }
                    catch (error) {
                        console.error(`❌ Failed to generate embedding for record ${record.id}:`, error);
                    }
                }
                if (i + batchSize < result.rows.length) {
                    console.log('⏳ Waiting before processing next batch...');
                    await new Promise(resolve => setTimeout(resolve, 2000));
                }
            }
            console.log(`✅ Completed processing ${tableName}`);
        }
        catch (error) {
            console.error(`❌ Failed to process ${tableName}:`, error);
            throw error;
        }
    }
    static getIdColumn(tableName) {
        const idColumns = {
            'Restaurants': 'restaurant_id',
            'Branches': 'branch_id',
            'Shops': 'shop_id',
            'ShopBranches': 'branch_id',
            'Clubs': 'club_id',
            'Items': 'item_id',
            'ShopItems': 'item_id',
            'Categories': 'category_id',
            'ShopCategories': 'category_id',
            'Facilities': 'facility_id'
        };
        return idColumns[tableName] || 'id';
    }
    static async checkHealth() {
        console.log('🏥 Checking vector database health...');
        try {
            const extensionResult = await (0, database_1.query)(`
        SELECT * FROM pg_extension WHERE extname = 'vector'
      `);
            if (extensionResult.rows.length === 0) {
                throw new Error('pgvector extension is not enabled');
            }
            console.log('✅ pgvector extension is enabled');
            const tables = ['Restaurants', 'Branches', 'Shops', 'Clubs'];
            for (const table of tables) {
                const columnResult = await (0, database_1.query)(`
          SELECT column_name 
          FROM information_schema.columns 
          WHERE table_name = $1 AND column_name = 'embedding'
        `, [table]);
                if (columnResult.rows.length > 0) {
                    console.log(`✅ Embedding column exists in ${table}`);
                }
                else {
                    console.log(`⚠️  Embedding column missing in ${table}`);
                }
            }
            const embeddingCount = await (0, database_1.query)(`
        SELECT COUNT(*) as count FROM "Restaurants" WHERE embedding IS NOT NULL
      `);
            console.log(`📊 Found ${embeddingCount.rows[0].count} restaurants with embeddings`);
            console.log('✅ Vector database health check completed');
        }
        catch (error) {
            console.error('❌ Vector database health check failed:', error);
            throw error;
        }
    }
}
exports.VectorDatabaseInitializer = VectorDatabaseInitializer;
exports.default = VectorDatabaseInitializer;
//# sourceMappingURL=initializeVectorDatabase.js.map