{"version": 3, "file": "errorHandler.js", "sourceRoot": "", "sources": ["../../src/middleware/errorHandler.ts"], "names": [], "mappings": ";;;AAKA,MAAa,QAAS,SAAQ,KAAK;IAKjC,YAAY,OAAe,EAAE,aAAqB,GAAG,EAAE,gBAAyB,IAAI,EAAE,IAAa;QACjG,KAAK,CAAC,OAAO,CAAC,CAAC;QACf,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QACnC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,IAAI,GAAG,UAAU,CAAC;QAGvB,KAAK,CAAC,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;IAClD,CAAC;CACF;AAfD,4BAeC;AAKD,MAAa,YAAa,SAAQ,QAAQ;IACxC,YAAY,OAAe,EAAE,IAAa;QACxC,KAAK,CAAC,sBAAsB,OAAO,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;QACxD,IAAI,CAAC,IAAI,GAAG,cAAc,CAAC;IAC7B,CAAC;CACF;AALD,oCAKC;AAED,MAAa,iBAAkB,SAAQ,QAAQ;IAC7C,YAAY,OAAe,EAAE,IAAa;QACxC,KAAK,CAAC,wBAAwB,OAAO,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;QAC1D,IAAI,CAAC,IAAI,GAAG,mBAAmB,CAAC;IAClC,CAAC;CACF;AALD,8CAKC;AAED,MAAa,aAAc,SAAQ,QAAQ;IACzC,YAAY,OAAe,EAAE,IAAa;QACxC,KAAK,CAAC,mBAAmB,OAAO,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;QACrD,IAAI,CAAC,IAAI,GAAG,eAAe,CAAC;IAC9B,CAAC;CACF;AALD,sCAKC;AAED,MAAa,eAAgB,SAAQ,QAAQ;IAC3C,YAAY,OAAe,EAAE,KAAc;QACzC,KAAK,CAAC,qBAAqB,OAAO,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;QACxD,IAAI,CAAC,IAAI,GAAG,iBAAiB,CAAC;IAChC,CAAC;CACF;AALD,0CAKC;AAED,MAAa,cAAe,SAAQ,QAAQ;IAC1C,YAAY,UAAkB,mBAAmB;QAC/C,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,qBAAqB,CAAC,CAAC;QACjD,IAAI,CAAC,IAAI,GAAG,gBAAgB,CAAC;IAC/B,CAAC;CACF;AALD,wCAKC;AAqBM,MAAM,YAAY,GAAG,CAC1B,KAAuB,EACvB,GAAY,EACZ,GAAa,EACb,IAAkB,EACZ,EAAE;IAER,OAAO,CAAC,KAAK,CAAC,iBAAiB,EAAE;QAC/B,OAAO,EAAE,KAAK,CAAC,OAAO;QACtB,KAAK,EAAE,KAAK,CAAC,KAAK;QAClB,IAAI,EAAE,GAAG,CAAC,IAAI;QACd,MAAM,EAAE,GAAG,CAAC,MAAM;QAClB,IAAI,EAAE,GAAG,CAAC,IAAI;QACd,KAAK,EAAE,GAAG,CAAC,KAAK;QAChB,MAAM,EAAE,GAAG,CAAC,MAAM;QAClB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;KACpC,CAAC,CAAC;IAGH,IAAI,UAAU,GAAG,GAAG,CAAC;IACrB,IAAI,OAAO,GAAG,uBAAuB,CAAC;IACtC,IAAI,IAAwB,CAAC;IAG7B,IAAI,KAAK,YAAY,QAAQ,EAAE,CAAC;QAC9B,UAAU,GAAG,KAAK,CAAC,UAAU,CAAC;QAC9B,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC;QACxB,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC;IACpB,CAAC;SAAM,IAAI,KAAK,CAAC,IAAI,KAAK,iBAAiB,EAAE,CAAC;QAC5C,UAAU,GAAG,GAAG,CAAC;QACjB,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC;QACxB,IAAI,GAAG,kBAAkB,CAAC;IAC5B,CAAC;SAAM,IAAI,KAAK,CAAC,IAAI,KAAK,WAAW,EAAE,CAAC;QACtC,UAAU,GAAG,GAAG,CAAC;QACjB,OAAO,GAAG,mBAAmB,CAAC;QAC9B,IAAI,GAAG,YAAY,CAAC;IACtB,CAAC;SAAM,IAAI,KAAK,CAAC,IAAI,KAAK,YAAY,IAAI,KAAK,CAAC,IAAI,KAAK,kBAAkB,EAAE,CAAC;QAC5E,UAAU,GAAG,GAAG,CAAC;QACjB,OAAO,GAAG,2BAA2B,CAAC;QACtC,IAAI,GAAG,gBAAgB,CAAC;IAC1B,CAAC;SAAM,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;QACzC,UAAU,GAAG,GAAG,CAAC;QACjB,OAAO,GAAG,8BAA8B,CAAC;QACzC,IAAI,GAAG,qBAAqB,CAAC;IAC/B,CAAC;IAGD,MAAM,aAAa,GAAkB;QACnC,OAAO,EAAE,KAAK;QACd,KAAK,EAAE;YACL,OAAO;YACP,IAAI;YACJ,UAAU;YACV,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,IAAI,EAAE,GAAG,CAAC,IAAI;YACd,MAAM,EAAE,GAAG,CAAC,MAAM;SACnB;KACF,CAAC;IAGF,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,EAAE,CAAC;QAC3C,aAAa,CAAC,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;IAC1C,CAAC;IAGD,GAAG,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;AAC7C,CAAC,CAAC;AAlEW,QAAA,YAAY,gBAkEvB;AAKK,MAAM,eAAe,GAAG,CAAC,GAAY,EAAE,GAAa,EAAQ,EAAE;IACnE,MAAM,aAAa,GAAkB;QACnC,OAAO,EAAE,KAAK;QACd,KAAK,EAAE;YACL,OAAO,EAAE,SAAS,GAAG,CAAC,MAAM,IAAI,GAAG,CAAC,IAAI,YAAY;YACpD,IAAI,EAAE,iBAAiB;YACvB,UAAU,EAAE,GAAG;YACf,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,IAAI,EAAE,GAAG,CAAC,IAAI;YACd,MAAM,EAAE,GAAG,CAAC,MAAM;SACnB;KACF,CAAC;IAEF,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;AACtC,CAAC,CAAC;AAdW,QAAA,eAAe,mBAc1B;AAKK,MAAM,YAAY,GAAG,CAAC,EAAY,EAAE,EAAE;IAC3C,OAAO,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;QACzD,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IAClD,CAAC,CAAC;AACJ,CAAC,CAAC;AAJW,QAAA,YAAY,gBAIvB;AAKK,MAAM,gBAAgB,GAAG,CAAC,KAAU,EAAE,SAAiB,EAAQ,EAAE;IACtE,IAAI,KAAK,KAAK,SAAS,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,EAAE,EAAE,CAAC;QAC1D,MAAM,IAAI,eAAe,CAAC,GAAG,SAAS,cAAc,EAAE,SAAS,CAAC,CAAC;IACnE,CAAC;AACH,CAAC,CAAC;AAJW,QAAA,gBAAgB,oBAI3B;AAEK,MAAM,cAAc,GAAG,CAAC,KAAU,EAAE,SAAiB,EAAE,SAAkB,EAAE,SAAkB,EAAQ,EAAE;IAC5G,IAAA,wBAAgB,EAAC,KAAK,EAAE,SAAS,CAAC,CAAC;IAEnC,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;QAC9B,MAAM,IAAI,eAAe,CAAC,GAAG,SAAS,mBAAmB,EAAE,SAAS,CAAC,CAAC;IACxE,CAAC;IAED,IAAI,SAAS,IAAI,KAAK,CAAC,MAAM,GAAG,SAAS,EAAE,CAAC;QAC1C,MAAM,IAAI,eAAe,CAAC,GAAG,SAAS,qBAAqB,SAAS,kBAAkB,EAAE,SAAS,CAAC,CAAC;IACrG,CAAC;IAED,IAAI,SAAS,IAAI,KAAK,CAAC,MAAM,GAAG,SAAS,EAAE,CAAC;QAC1C,MAAM,IAAI,eAAe,CAAC,GAAG,SAAS,yBAAyB,SAAS,kBAAkB,EAAE,SAAS,CAAC,CAAC;IACzG,CAAC;AACH,CAAC,CAAC;AAdW,QAAA,cAAc,kBAczB;AAEK,MAAM,cAAc,GAAG,CAAC,KAAU,EAAE,SAAiB,EAAE,GAAY,EAAE,GAAY,EAAQ,EAAE;IAChG,IAAI,KAAK,KAAK,SAAS,IAAI,KAAK,KAAK,IAAI,EAAE,CAAC;QAC1C,MAAM,QAAQ,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;QAE/B,IAAI,KAAK,CAAC,QAAQ,CAAC,EAAE,CAAC;YACpB,MAAM,IAAI,eAAe,CAAC,GAAG,SAAS,yBAAyB,EAAE,SAAS,CAAC,CAAC;QAC9E,CAAC;QAED,IAAI,GAAG,KAAK,SAAS,IAAI,QAAQ,GAAG,GAAG,EAAE,CAAC;YACxC,MAAM,IAAI,eAAe,CAAC,GAAG,SAAS,qBAAqB,GAAG,EAAE,EAAE,SAAS,CAAC,CAAC;QAC/E,CAAC;QAED,IAAI,GAAG,KAAK,SAAS,IAAI,QAAQ,GAAG,GAAG,EAAE,CAAC;YACxC,MAAM,IAAI,eAAe,CAAC,GAAG,SAAS,yBAAyB,GAAG,EAAE,EAAE,SAAS,CAAC,CAAC;QACnF,CAAC;IACH,CAAC;AACH,CAAC,CAAC;AAhBW,QAAA,cAAc,kBAgBzB;AAEK,MAAM,aAAa,GAAG,CAAC,KAAU,EAAE,SAAiB,EAAE,SAAkB,EAAE,SAAkB,EAAQ,EAAE;IAC3G,IAAI,KAAK,KAAK,SAAS,IAAI,KAAK,KAAK,IAAI,EAAE,CAAC;QAC1C,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;YAC1B,MAAM,IAAI,eAAe,CAAC,GAAG,SAAS,mBAAmB,EAAE,SAAS,CAAC,CAAC;QACxE,CAAC;QAED,IAAI,SAAS,IAAI,KAAK,CAAC,MAAM,GAAG,SAAS,EAAE,CAAC;YAC1C,MAAM,IAAI,eAAe,CAAC,GAAG,SAAS,0BAA0B,SAAS,QAAQ,EAAE,SAAS,CAAC,CAAC;QAChG,CAAC;QAED,IAAI,SAAS,IAAI,KAAK,CAAC,MAAM,GAAG,SAAS,EAAE,CAAC;YAC1C,MAAM,IAAI,eAAe,CAAC,GAAG,SAAS,8BAA8B,SAAS,QAAQ,EAAE,SAAS,CAAC,CAAC;QACpG,CAAC;IACH,CAAC;AACH,CAAC,CAAC;AAdW,QAAA,aAAa,iBAcxB;AAKW,QAAA,MAAM,GAAG;IACpB,KAAK,EAAE,CAAC,OAAe,EAAE,KAAa,EAAE,OAAa,EAAE,EAAE;QACvD,OAAO,CAAC,KAAK,CAAC,WAAW,OAAO,EAAE,EAAE;YAClC,KAAK,EAAE,KAAK,EAAE,OAAO;YACrB,KAAK,EAAE,KAAK,EAAE,KAAK;YACnB,OAAO;YACP,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CAAC,CAAC;IACL,CAAC;IAED,IAAI,EAAE,CAAC,OAAe,EAAE,OAAa,EAAE,EAAE;QACvC,OAAO,CAAC,IAAI,CAAC,UAAU,OAAO,EAAE,EAAE;YAChC,OAAO;YACP,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CAAC,CAAC;IACL,CAAC;IAED,IAAI,EAAE,CAAC,OAAe,EAAE,OAAa,EAAE,EAAE;QACvC,OAAO,CAAC,GAAG,CAAC,UAAU,OAAO,EAAE,EAAE;YAC/B,OAAO;YACP,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CAAC,CAAC;IACL,CAAC;IAED,KAAK,EAAE,CAAC,OAAe,EAAE,OAAa,EAAE,EAAE;QACxC,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,EAAE,CAAC;YAC3C,OAAO,CAAC,GAAG,CAAC,WAAW,OAAO,EAAE,EAAE;gBAChC,OAAO;gBACP,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACpC,CAAC,CAAC;QACL,CAAC;IACH,CAAC;CACF,CAAC"}