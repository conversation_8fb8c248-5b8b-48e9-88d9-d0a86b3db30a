"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DatabaseController = void 0;
const database_service_1 = require("../services/database.service");
class DatabaseController {
    static async testConnection(_req, res) {
        try {
            const isConnected = await database_service_1.DatabaseService.testConnection();
            if (isConnected) {
                res.status(200).json({
                    success: true,
                    message: 'Database connection successful',
                    timestamp: new Date().toISOString()
                });
            }
            else {
                res.status(500).json({
                    success: false,
                    message: 'Database connection failed',
                    timestamp: new Date().toISOString()
                });
            }
        }
        catch (error) {
            console.error('Database connection test error:', error);
            res.status(500).json({
                success: false,
                message: 'Database connection test failed',
                error: error instanceof Error ? error.message : 'Unknown error',
                timestamp: new Date().toISOString()
            });
        }
    }
    static async getDatabaseInfo(_req, res) {
        try {
            const version = await database_service_1.DatabaseService.getDatabaseVersion();
            res.status(200).json({
                success: true,
                data: {
                    version,
                    connected: true
                },
                timestamp: new Date().toISOString()
            });
        }
        catch (error) {
            console.error('Get database info error:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to get database information',
                error: error instanceof Error ? error.message : 'Unknown error',
                timestamp: new Date().toISOString()
            });
        }
    }
    static async executeQuery(req, res) {
        try {
            const { query: sqlQuery, params } = req.body;
            if (!sqlQuery) {
                res.status(400).json({
                    success: false,
                    message: 'SQL query is required',
                    timestamp: new Date().toISOString()
                });
                return;
            }
            if (!sqlQuery.trim().toLowerCase().startsWith('select')) {
                res.status(400).json({
                    success: false,
                    message: 'Only SELECT queries are allowed',
                    timestamp: new Date().toISOString()
                });
                return;
            }
            const result = await database_service_1.DatabaseService.executeQuery(sqlQuery, params);
            res.status(200).json({
                success: true,
                data: {
                    rows: result.rows,
                    rowCount: result.rowCount,
                    fields: result.fields?.map((field) => ({
                        name: field.name,
                        dataTypeID: field.dataTypeID
                    }))
                },
                timestamp: new Date().toISOString()
            });
        }
        catch (error) {
            console.error('Execute query error:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to execute query',
                error: error instanceof Error ? error.message : 'Unknown error',
                timestamp: new Date().toISOString()
            });
        }
    }
}
exports.DatabaseController = DatabaseController;
//# sourceMappingURL=database.controller.js.map