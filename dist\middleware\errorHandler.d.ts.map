{"version": 3, "file": "errorHandler.d.ts", "sourceRoot": "", "sources": ["../../src/middleware/errorHandler.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,YAAY,EAAE,MAAM,SAAS,CAAC;AAK1D,qBAAa,QAAS,SAAQ,KAAK;IAC1B,UAAU,EAAE,MAAM,CAAC;IACnB,aAAa,EAAE,OAAO,CAAC;IACvB,IAAI,CAAC,EAAE,MAAM,CAAC;gBAET,OAAO,EAAE,MAAM,EAAE,UAAU,GAAE,MAAY,EAAE,aAAa,GAAE,OAAc,EAAE,IAAI,CAAC,EAAE,MAAM;CAUpG;AAKD,qBAAa,YAAa,SAAQ,QAAQ;gBAC5B,OAAO,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,MAAM;CAI3C;AAED,qBAAa,iBAAkB,SAAQ,QAAQ;gBACjC,OAAO,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,MAAM;CAI3C;AAED,qBAAa,aAAc,SAAQ,QAAQ;gBAC7B,OAAO,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,MAAM;CAI3C;AAED,qBAAa,eAAgB,SAAQ,QAAQ;gBAC/B,OAAO,EAAE,MAAM,EAAE,KAAK,CAAC,EAAE,MAAM;CAI5C;AAED,qBAAa,cAAe,SAAQ,QAAQ;gBAC9B,OAAO,GAAE,MAA4B;CAIlD;AAqBD,eAAO,MAAM,YAAY,GACvB,OAAO,KAAK,GAAG,QAAQ,EACvB,KAAK,OAAO,EACZ,KAAK,QAAQ,EACb,MAAM,YAAY,KACjB,IA6DF,CAAC;AAKF,eAAO,MAAM,eAAe,GAAI,KAAK,OAAO,EAAE,KAAK,QAAQ,KAAG,IAc7D,CAAC;AAKF,eAAO,MAAM,YAAY,GAAI,IAAI,QAAQ,MAC/B,KAAK,OAAO,EAAE,KAAK,QAAQ,EAAE,MAAM,YAAY,SAGxD,CAAC;AAKF,eAAO,MAAM,gBAAgB,GAAI,OAAO,GAAG,EAAE,WAAW,MAAM,KAAG,IAIhE,CAAC;AAEF,eAAO,MAAM,cAAc,GAAI,OAAO,GAAG,EAAE,WAAW,MAAM,EAAE,YAAY,MAAM,EAAE,YAAY,MAAM,KAAG,IActG,CAAC;AAEF,eAAO,MAAM,cAAc,GAAI,OAAO,GAAG,EAAE,WAAW,MAAM,EAAE,MAAM,MAAM,EAAE,MAAM,MAAM,KAAG,IAgB1F,CAAC;AAEF,eAAO,MAAM,aAAa,GAAI,OAAO,GAAG,EAAE,WAAW,MAAM,EAAE,YAAY,MAAM,EAAE,YAAY,MAAM,KAAG,IAcrG,CAAC;AAKF,eAAO,MAAM,MAAM;qBACA,MAAM,UAAU,KAAK,YAAY,GAAG;oBASrC,MAAM,YAAY,GAAG;oBAOrB,MAAM,YAAY,GAAG;qBAOpB,MAAM,YAAY,GAAG;CAQvC,CAAC"}