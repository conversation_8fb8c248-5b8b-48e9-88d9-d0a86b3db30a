import { VectorSearchResult } from '../models/Business';
import { logger } from '../middleware/errorHandler';

/**
 * Interface for WhatsApp message response
 */
export interface WhatsAppResponse {
  messaging_product: 'whatsapp';
  to: string;
  type: 'text' | 'interactive';
  text?: {
    body: string;
    preview_url?: boolean;
  };
  interactive?: {
    type: 'button' | 'list';
    header?: {
      type: 'text';
      text: string;
    };
    body: {
      text: string;
    };
    footer?: {
      text: string;
    };
    action: {
      buttons?: Array<{
        type: 'reply';
        reply: {
          id: string;
          title: string;
        };
      }>;
      sections?: Array<{
        title: string;
        rows: Array<{
          id: string;
          title: string;
          description?: string;
        }>;
      }>;
    };
  };
}

/**
 * WhatsApp response formatter service
 */
export class WhatsAppResponseFormatter {

  /**
   * Format AI response with business recommendations for WhatsApp
   */
  static formatAIResponse(
    aiResponse: string,
    businessRecommendations: VectorSearchResult[],
    recipientPhoneNumber: string,
    suggestedFollowUps?: string[]
  ): WhatsAppResponse[] {
    const responses: WhatsAppResponse[] = [];

    try {
      // Main AI response message
      const mainResponse = this.formatTextMessage(aiResponse, recipientPhoneNumber);
      responses.push(mainResponse);

      // Business recommendations as interactive message if available
      if (businessRecommendations.length > 0) {
        const businessMessage = this.formatBusinessRecommendations(
          businessRecommendations,
          recipientPhoneNumber
        );
        responses.push(businessMessage);
      }

      // Follow-up suggestions as buttons if available
      if (suggestedFollowUps && suggestedFollowUps.length > 0) {
        const followUpMessage = this.formatFollowUpSuggestions(
          suggestedFollowUps,
          recipientPhoneNumber
        );
        responses.push(followUpMessage);
      }

      logger.info('Formatted WhatsApp responses', {
        recipientPhoneNumber,
        responseCount: responses.length,
        businessCount: businessRecommendations.length
      });

      return responses;

    } catch (error) {
      logger.error('Error formatting WhatsApp response', error instanceof Error ? error : new Error(String(error)));
      
      // Return fallback response
      return [this.formatTextMessage(
        'I apologize, but I encountered an error while processing your request. Please try again.',
        recipientPhoneNumber
      )];
    }
  }

  /**
   * Format simple text message
   */
  static formatTextMessage(text: string, recipientPhoneNumber: string): WhatsAppResponse {
    return {
      messaging_product: 'whatsapp',
      to: recipientPhoneNumber,
      type: 'text',
      text: {
        body: this.truncateText(text, 4096), // WhatsApp text message limit
        preview_url: true
      }
    };
  }

  /**
   * Format business recommendations as interactive list
   */
  private static formatBusinessRecommendations(
    recommendations: VectorSearchResult[],
    recipientPhoneNumber: string
  ): WhatsAppResponse {
    // Limit to top 10 recommendations for WhatsApp list
    const topRecommendations = recommendations.slice(0, 10);

    const sections = [{
      title: 'Recommended Businesses',
      rows: topRecommendations.map((result, index) => {
        const entity = result.entity;
        const title = this.truncateText(entity.name, 24); // WhatsApp title limit
        
        let description = '';
        
        // Build description based on entity type
        switch (result.entity_type) {
          case 'restaurant':
          case 'branch':
            description = `${entity.branch_address || entity.club_location || 'Dubai'}`;
            if (entity.average_spend) {
              description += ` • AED ${entity.average_spend} avg`;
            }
            if (entity.phone_number) {
              description += ` • ${entity.phone_number}`;
            }
            break;
            
          case 'shop':
          case 'shop_branch':
            description = `${entity.branch_address || 'Dubai'}`;
            if (entity.phone_number) {
              description += ` • ${entity.phone_number}`;
            }
            break;
            
          case 'club':
            description = `${entity.club_location || 'Dubai'}`;
            if (entity.average_spend) {
              description += ` • AED ${entity.average_spend} avg`;
            }
            if (entity.phone_number) {
              description += ` • ${entity.phone_number}`;
            }
            break;
            
          case 'food_item':
            description = `AED ${entity.item_price}`;
            if (entity.restaurant_name) {
              description += ` • ${entity.restaurant_name}`;
            }
            break;
            
          case 'facility':
            description = `${entity.facility_type} • ${entity.club_name}`;
            break;
            
          default:
            description = entity.description || 'Business in Dubai';
        }

        return {
          id: `business_${index}_${entity.id}`,
          title,
          description: this.truncateText(description, 72) // WhatsApp description limit
        };
      })
    }];

    return {
      messaging_product: 'whatsapp',
      to: recipientPhoneNumber,
      type: 'interactive',
      interactive: {
        type: 'list',
        header: {
          type: 'text',
          text: '🏢 Business Recommendations'
        },
        body: {
          text: 'Here are some businesses that match your query. Tap on any business to get more details.'
        },
        footer: {
          text: 'Powered by Cravin Concierge'
        },
        action: {
          sections
        }
      }
    };
  }

  /**
   * Format follow-up suggestions as buttons
   */
  private static formatFollowUpSuggestions(
    suggestions: string[],
    recipientPhoneNumber: string
  ): WhatsAppResponse {
    // WhatsApp supports max 3 buttons
    const buttons = suggestions.slice(0, 3).map((suggestion, index) => ({
      type: 'reply' as const,
      reply: {
        id: `followup_${index}`,
        title: this.truncateText(suggestion, 20) // WhatsApp button title limit
      }
    }));

    return {
      messaging_product: 'whatsapp',
      to: recipientPhoneNumber,
      type: 'interactive',
      interactive: {
        type: 'button',
        body: {
          text: 'Would you like to know more? Choose one of these options:'
        },
        action: {
          buttons
        }
      }
    };
  }

  /**
   * Format business details message
   */
  static formatBusinessDetails(
    business: VectorSearchResult,
    recipientPhoneNumber: string
  ): WhatsAppResponse {
    const entity = business.entity;
    let details = `*${entity.name}*\n\n`;

    // Add description
    if (entity.description) {
      details += `${entity.description}\n\n`;
    }

    // Add contact information
    if (entity.phone_number) {
      details += `📞 *Phone:* ${entity.phone_number}\n`;
    }

    // Add location information
    if (entity.branch_address) {
      details += `📍 *Address:* ${entity.branch_address}\n`;
    } else if (entity.club_location) {
      details += `📍 *Location:* ${entity.club_location}\n`;
    }

    // Add emirate
    if (entity.branch_emirate || entity.club_emirate) {
      details += `🏙️ *Emirate:* ${entity.branch_emirate || entity.club_emirate}\n`;
    }

    // Add pricing information
    if (entity.average_spend) {
      details += `💰 *Average Spend:* AED ${entity.average_spend}\n`;
    } else if (entity.item_price) {
      details += `💰 *Price:* AED ${entity.item_price}\n`;
    }

    // Add specific details based on entity type
    switch (business.entity_type) {
      case 'restaurant':
        if (entity.restaurant_menu_type) {
          details += `🍽️ *Cuisine:* ${entity.restaurant_menu_type}\n`;
        }
        if (entity.take_orders) {
          details += `📱 *Online Orders:* Available\n`;
        }
        break;

      case 'club':
        if (entity.take_booking) {
          details += `📅 *Bookings:* Available\n`;
        }
        break;

      case 'facility':
        if (entity.facility_type) {
          details += `🏃 *Facility Type:* ${entity.facility_type}\n`;
        }
        if (entity.facility_category) {
          details += `🎯 *Category:* ${entity.facility_category}\n`;
        }
        break;
    }

    // Add tags if available
    const tags = entity.branch_tags || entity.club_tags;
    if (tags && tags.length > 0) {
      details += `🏷️ *Tags:* ${tags.join(', ')}\n`;
    }

    // Add maps URL if available
    if (entity.branch_maps_url) {
      details += `\n🗺️ View on Maps: ${entity.branch_maps_url}`;
    }

    return this.formatTextMessage(details, recipientPhoneNumber);
  }

  /**
   * Format error message
   */
  static formatErrorMessage(
    error: string,
    recipientPhoneNumber: string
  ): WhatsAppResponse {
    const errorMessage = `❌ *Error*\n\n${error}\n\nPlease try again or contact support if the problem persists.`;
    return this.formatTextMessage(errorMessage, recipientPhoneNumber);
  }

  /**
   * Format welcome message
   */
  static formatWelcomeMessage(recipientPhoneNumber: string): WhatsAppResponse {
    const welcomeText = `👋 *Welcome to Cravin Concierge!*

I'm your AI assistant for discovering amazing local businesses in Dubai and the UAE.

You can ask me about:
🍽️ Restaurants and food
🛍️ Shops and stores  
🎉 Clubs and nightlife
🏃 Sports facilities
📍 Businesses near you

Just send me a message like:
• "Find Italian restaurants in Dubai Marina"
• "Best coffee shops near me"
• "Gyms in Jumeirah"

How can I help you today?`;

    return this.formatTextMessage(welcomeText, recipientPhoneNumber);
  }

  /**
   * Truncate text to specified length
   */
  private static truncateText(text: string, maxLength: number): string {
    if (text.length <= maxLength) {
      return text;
    }
    return text.substring(0, maxLength - 3) + '...';
  }

  /**
   * Clean phone number for WhatsApp format
   */
  static cleanPhoneNumber(phoneNumber: string): string {
    // Remove any non-digit characters
    const cleaned = phoneNumber.replace(/\D/g, '');
    
    // Ensure it starts with country code (assuming UAE +971 if not present)
    if (!cleaned.startsWith('971') && cleaned.length === 9) {
      return '971' + cleaned;
    }
    
    return cleaned;
  }
}
