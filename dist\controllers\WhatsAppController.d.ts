import { Request, Response } from 'express';
export declare class WhatsAppController {
    verifyWebhook: (req: Request, res: Response) => void;
    handleWebhook: (req: Request, res: Response) => Promise<void>;
    private processMessage;
    private handleButtonResponse;
    private handleBusinessSelection;
    private sendWelcomeMessage;
    private sendAIResponse;
    private sendUnsupportedMessageResponse;
    private sendErrorResponse;
    private sendWhatsAppMessage;
}
//# sourceMappingURL=WhatsAppController.d.ts.map