# WhatsApp Conversational AI Integration

This document describes the WhatsApp conversational AI bot integration for the Cravin Concierge application.

## Overview

The WhatsApp AI integration provides intelligent business recommendations through WhatsApp messages. Users can send natural language queries about local businesses in Dubai and the UAE, and receive AI-powered responses with relevant business recommendations using vector search capabilities.

## Architecture

### Core Components

1. **WhatsApp Webhook Handler** (`src/controllers/WhatsAppController.ts`)
   - Processes incoming WhatsApp webhook requests
   - Handles message parsing and response formatting
   - Manages user sessions and conversation context

2. **Message Parser** (`src/services/WhatsAppMessageParser.ts`)
   - Parses WhatsApp webhook payloads
   - Extracts user messages, contact info, and metadata
   - Handles different message types (text, location, image, etc.)

3. **Response Formatter** (`src/services/WhatsAppResponseFormatter.ts`)
   - Formats AI responses for WhatsApp message format
   - Creates interactive messages with business recommendations
   - Handles text truncation and WhatsApp API limits

4. **Conversational AI Service** (`src/services/ConversationalAIService.ts`)
   - Processes user queries through AI and vector search
   - Maintains conversation context and history
   - Generates intelligent responses with business recommendations

5. **Vector Search Service** (`src/services/VectorSearchService.ts`)
   - Performs similarity search on business data using pgvector
   - Searches across restaurants, shops, clubs, and facilities
   - Returns ranked business recommendations

6. **AWS Bedrock Service** (`src/services/BedrockService.ts`)
   - Generates AI responses using Claude models
   - Creates embeddings for vector search
   - Handles AWS Bedrock API interactions

## Features

### Message Processing
- **Text Messages**: Natural language queries about businesses
- **Location Messages**: Find businesses near shared locations
- **Interactive Responses**: Handle button clicks and list selections
- **Session Management**: Maintain conversation context per user

### AI Capabilities
- **Natural Language Understanding**: Process conversational queries
- **Business Recommendations**: Vector-based similarity search
- **Context Awareness**: Remember previous conversations
- **Follow-up Suggestions**: Provide relevant next questions

### WhatsApp Integration
- **Webhook Verification**: Secure webhook endpoint verification
- **Message Parsing**: Handle various WhatsApp message formats
- **Interactive Messages**: Send buttons and lists for better UX
- **Error Handling**: Graceful error responses to users

## Setup Instructions

### 1. Environment Configuration

Add the following environment variables to your `.env` file:

```env
# WhatsApp Configuration
WHATSAPP_VERIFY_TOKEN=your_webhook_verify_token

# AWS Bedrock Configuration
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
BEDROCK_MODEL_ID=anthropic.claude-3-sonnet-20240229-v1:0
BEDROCK_MAX_TOKENS=4000
BEDROCK_TEMPERATURE=0.7

# Vector Search Configuration
VECTOR_SIMILARITY_THRESHOLD=0.7
VECTOR_MAX_RESULTS=5
EMBEDDING_DIMENSIONS=1536

# Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/cravin_concierge
```

### 2. Database Setup

Initialize the vector database:

```bash
# Install dependencies
npm install

# Initialize vector database with pgvector extension
npm run vector-init

# Generate embeddings for existing business data
npm run vector-embeddings
```

### 3. WhatsApp Business API Setup

1. Create a WhatsApp Business Account
2. Set up a WhatsApp Business API application
3. Configure webhook URL: `https://your-domain.com/api/whatsapp/webhook`
4. Set webhook verify token in environment variables
5. Subscribe to message events

### 4. AWS Bedrock Setup

1. Enable AWS Bedrock in your AWS account
2. Request access to Claude models
3. Configure AWS credentials with Bedrock permissions
4. Test connection with health check

## API Endpoints

### WhatsApp Webhook

#### GET /api/whatsapp/webhook
Webhook verification endpoint for WhatsApp.

**Query Parameters:**
- `hub.mode`: Subscription mode
- `hub.challenge`: Challenge string to echo back
- `hub.verify_token`: Verification token

#### POST /api/whatsapp/webhook
Handles incoming WhatsApp messages.

**Request Body:**
WhatsApp webhook payload with message data.

**Response:**
```json
{
  "success": true,
  "message": "Webhook processed successfully",
  "processedMessages": 1
}
```

## Usage Examples

### Text Query
**User:** "Find me Italian restaurants in Dubai Marina"

**AI Response:** 
- Conversational response about Italian restaurants
- Interactive list of recommended restaurants
- Follow-up suggestion buttons

### Location Query
**User:** *Shares location*

**AI Response:**
- "I can see you're at [location]. What businesses are you looking for nearby?"
- Contextual recommendations based on location

### Follow-up Questions
**User:** "What are their operating hours?"

**AI Response:**
- Specific information about previously mentioned businesses
- Additional relevant details

## Testing

### Run Integration Tests
```bash
npm run test-whatsapp-ai
```

### Manual Testing
1. Set up ngrok for local webhook testing:
   ```bash
   ngrok http 3000
   ```

2. Configure WhatsApp webhook URL with ngrok URL

3. Send test messages to your WhatsApp Business number

4. Monitor logs for message processing

### Health Checks
```bash
# Check vector database health
npm run vector-health

# Check API health
curl http://localhost:3000/api/health
```

## Message Flow

1. **Incoming Message**
   - WhatsApp sends webhook to `/api/whatsapp/webhook`
   - Message parsed and validated
   - User session identified by phone number

2. **AI Processing**
   - Query extracted from message
   - Vector search performed on business database
   - AI generates conversational response
   - Business recommendations ranked by relevance

3. **Response Formatting**
   - AI response formatted for WhatsApp
   - Interactive elements added (buttons, lists)
   - Multiple messages sent if needed

4. **Context Management**
   - Conversation history updated
   - User preferences stored
   - Session maintained for follow-up queries

## Error Handling

- **Invalid Messages**: Helpful responses for unsupported formats
- **AI Service Errors**: Fallback responses when AI is unavailable
- **Database Errors**: Graceful degradation with basic responses
- **Rate Limiting**: Automatic retry with exponential backoff

## Performance Considerations

- **Vector Search**: Optimized with HNSW indexes
- **Response Time**: Target < 3 seconds for AI responses
- **Concurrent Users**: Stateless design supports multiple sessions
- **Memory Usage**: Context cleanup after inactivity

## Security

- **Webhook Verification**: Validates WhatsApp webhook signatures
- **Input Sanitization**: Prevents injection attacks
- **Rate Limiting**: Protects against abuse
- **Error Masking**: Doesn't expose internal errors to users

## Monitoring

- **Structured Logging**: All interactions logged with context
- **Error Tracking**: Comprehensive error logging and alerting
- **Performance Metrics**: Response times and success rates
- **Usage Analytics**: Query patterns and user engagement

## Troubleshooting

### Common Issues

1. **Webhook Verification Fails**
   - Check WHATSAPP_VERIFY_TOKEN matches WhatsApp configuration
   - Ensure webhook URL is accessible

2. **AI Responses Not Working**
   - Verify AWS Bedrock credentials and permissions
   - Check model availability in your region

3. **No Business Recommendations**
   - Ensure vector database is initialized
   - Check if embeddings are generated for business data

4. **Messages Not Processed**
   - Check webhook payload format
   - Verify message parsing logic

### Debug Commands

```bash
# Test message parsing
node -e "console.log(require('./dist/services/WhatsAppMessageParser').WhatsAppMessageParser.parseWebhookPayload(payload))"

# Test AI service
npm run test-whatsapp-ai

# Check database connection
npm run vector-health
```

## Future Enhancements

- **Multi-language Support**: Arabic and other languages
- **Rich Media**: Image and video responses
- **Booking Integration**: Direct booking through WhatsApp
- **Payment Processing**: In-chat payment capabilities
- **Analytics Dashboard**: Usage and performance metrics
- **A/B Testing**: Response optimization

## Support

For technical support or questions about the WhatsApp AI integration:

1. Check the logs for error details
2. Run the test suite to identify issues
3. Review the troubleshooting section
4. Contact the development team with specific error messages and context
