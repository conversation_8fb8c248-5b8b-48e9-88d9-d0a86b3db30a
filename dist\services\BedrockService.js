"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.bedrockService = exports.BedrockService = void 0;
const client_bedrock_runtime_1 = require("@aws-sdk/client-bedrock-runtime");
const environment_1 = require("../config/environment");
const errorHandler_1 = require("../middleware/errorHandler");
class BedrockService {
    constructor() {
        this.client = new client_bedrock_runtime_1.BedrockRuntimeClient({
            region: environment_1.config.aws.region,
            credentials: {
                accessKeyId: environment_1.config.aws.accessKeyId,
                secretAccessKey: environment_1.config.aws.secretAccessKey,
            },
        });
    }
    async generateResponse(prompt, systemPrompt) {
        try {
            const messages = [
                {
                    role: 'user',
                    content: prompt,
                },
            ];
            const requestBody = {
                anthropic_version: 'bedrock-2023-05-31',
                max_tokens: environment_1.config.bedrock.maxTokens,
                temperature: environment_1.config.bedrock.temperature,
                messages,
                ...(systemPrompt && { system: systemPrompt }),
            };
            const input = {
                modelId: environment_1.config.bedrock.modelId,
                contentType: 'application/json',
                accept: 'application/json',
                body: JSON.stringify(requestBody),
            };
            const command = new client_bedrock_runtime_1.InvokeModelCommand(input);
            const response = await this.client.send(command);
            if (!response.body) {
                throw new Error('No response body from Bedrock');
            }
            const responseBody = JSON.parse(new TextDecoder().decode(response.body));
            if (responseBody.content && responseBody.content.length > 0) {
                return responseBody.content[0].text;
            }
            throw new errorHandler_1.BedrockError('Invalid response format from Bedrock');
        }
        catch (error) {
            errorHandler_1.logger.error('Error generating AI response', error instanceof Error ? error : new Error(String(error)));
            if (error instanceof errorHandler_1.BedrockError) {
                throw error;
            }
            throw new errorHandler_1.BedrockError(`Failed to generate AI response: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    async generateEmbedding(text) {
        try {
            const requestBody = {
                inputText: text,
            };
            const input = {
                modelId: 'amazon.titan-embed-text-v1',
                contentType: 'application/json',
                accept: 'application/json',
                body: JSON.stringify(requestBody),
            };
            const command = new client_bedrock_runtime_1.InvokeModelCommand(input);
            const response = await this.client.send(command);
            if (!response.body) {
                throw new Error('No response body from Bedrock');
            }
            const responseBody = JSON.parse(new TextDecoder().decode(response.body));
            if (responseBody.embedding && Array.isArray(responseBody.embedding)) {
                return responseBody.embedding;
            }
            throw new errorHandler_1.BedrockError('Invalid embedding response format from Bedrock');
        }
        catch (error) {
            errorHandler_1.logger.error('Error generating embedding', error instanceof Error ? error : new Error(String(error)));
            if (error instanceof errorHandler_1.BedrockError) {
                throw error;
            }
            throw new errorHandler_1.BedrockError(`Failed to generate embedding: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    async generateEmbeddings(texts) {
        try {
            const embeddings = [];
            const batchSize = 5;
            for (let i = 0; i < texts.length; i += batchSize) {
                const batch = texts.slice(i, i + batchSize);
                const batchPromises = batch.map(text => this.generateEmbedding(text));
                const batchResults = await Promise.all(batchPromises);
                embeddings.push(...batchResults);
                if (i + batchSize < texts.length) {
                    await new Promise(resolve => setTimeout(resolve, 100));
                }
            }
            return embeddings;
        }
        catch (error) {
            errorHandler_1.logger.error('Error generating batch embeddings', error instanceof Error ? error : new Error(String(error)));
            if (error instanceof errorHandler_1.BedrockError) {
                throw error;
            }
            throw new errorHandler_1.BedrockError(`Failed to generate batch embeddings: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    createBusinessRecommendationPrompt(userQuery, businessData, conversationContext) {
        const contextSection = conversationContext
            ? `Previous conversation context:\n${conversationContext}\n\n`
            : '';
        const businessSection = businessData.length > 0
            ? `Here are some relevant businesses I found:\n${businessData.map((business, index) => `${index + 1}. ${business.name || business.restaurant_name || business.shop_name || business.club_name}
             - Type: ${business.entity_type || 'business'}
             - Description: ${business.description || business.branch_description || 'No description available'}
             - Location: ${business.branch_address || business.club_location || 'Location not specified'}
             - Phone: ${business.phone_number || 'Not available'}
             ${business.average_spend ? `- Average spend: AED ${business.average_spend}` : ''}
             ${business.branch_tags ? `- Tags: ${business.branch_tags.join(', ')}` : ''}
             ${business.club_tags ? `- Tags: ${business.club_tags.join(', ')}` : ''}
          `).join('\n\n')}\n\n`
            : 'I couldn\'t find any specific businesses matching your query in my database.\n\n';
        return `${contextSection}User query: "${userQuery}"

${businessSection}Please provide a helpful, conversational response about local businesses in Dubai/UAE. If you found relevant businesses above, recommend them naturally and mention their key details. If no specific businesses were found, provide general guidance about the type of businesses they're looking for and suggest they try different search terms.

Keep your response:
- Conversational and friendly
- Focused on helping them find what they need
- Include practical information like location, contact details, and pricing when available
- Suggest alternatives if the exact request isn't available
- Maximum 200 words

Response:`;
    }
    getSystemPrompt() {
        return `You are Cravin Concierge, a helpful AI assistant specializing in local business recommendations in Dubai and the UAE. You help users discover restaurants, shops, clubs, and other local businesses.

Your role:
- Provide personalized business recommendations based on user queries
- Share practical information like location, contact details, pricing, and operating hours
- Maintain a friendly, conversational tone
- Focus on helping users make informed decisions about local businesses
- If you don't have specific information, be honest and suggest alternatives

Guidelines:
- Always be helpful and professional
- Provide accurate information based on the business data available
- Suggest alternatives when exact matches aren't found
- Keep responses concise but informative
- Include contact information and location details when available`;
    }
}
exports.BedrockService = BedrockService;
exports.bedrockService = new BedrockService();
//# sourceMappingURL=BedrockService.js.map