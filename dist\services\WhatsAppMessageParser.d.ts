export interface WhatsAppMessage {
    messageId: string;
    from: string;
    to: string;
    timestamp: string;
    type: 'text' | 'image' | 'audio' | 'video' | 'document' | 'location' | 'contacts' | 'unknown';
    text?: string;
    image?: {
        id: string;
        mime_type: string;
        sha256: string;
        caption?: string;
    };
    location?: {
        latitude: number;
        longitude: number;
        name?: string;
        address?: string;
    };
    contact?: {
        name: string;
        phone: string;
    };
    context?: {
        from: string;
        id: string;
    };
}
export interface WhatsAppWebhookPayload {
    object: string;
    entry: Array<{
        id: string;
        changes: Array<{
            value: {
                messaging_product: string;
                metadata: {
                    display_phone_number: string;
                    phone_number_id: string;
                };
                contacts?: Array<{
                    profile: {
                        name: string;
                    };
                    wa_id: string;
                }>;
                messages?: Array<{
                    from: string;
                    id: string;
                    timestamp: string;
                    type: string;
                    text?: {
                        body: string;
                    };
                    image?: {
                        caption?: string;
                        mime_type: string;
                        sha256: string;
                        id: string;
                    };
                    location?: {
                        latitude: number;
                        longitude: number;
                        name?: string;
                        address?: string;
                    };
                    contacts?: Array<{
                        name: {
                            formatted_name: string;
                        };
                        phones: Array<{
                            phone: string;
                            type: string;
                        }>;
                    }>;
                    context?: {
                        from: string;
                        id: string;
                    };
                }>;
                statuses?: Array<{
                    id: string;
                    status: string;
                    timestamp: string;
                    recipient_id: string;
                }>;
            };
            field: string;
        }>;
    }>;
}
export interface WhatsAppUserProfile {
    phoneNumber: string;
    name?: string;
    displayPhoneNumber: string;
    phoneNumberId: string;
}
export declare class WhatsAppMessageParser {
    static parseWebhookPayload(payload: any): {
        messages: WhatsAppMessage[];
        userProfiles: WhatsAppUserProfile[];
    };
    private static parseMessage;
    private static normalizeMessageType;
    static extractUserQuery(message: WhatsAppMessage): string | null;
    static generateSessionId(phoneNumber: string): string;
    static extractUserPreferences(message: WhatsAppMessage, userProfile?: WhatsAppUserProfile): any;
    static validateWebhookSignature(payload: string, signature: string, secret: string): boolean;
}
//# sourceMappingURL=WhatsAppMessageParser.d.ts.map