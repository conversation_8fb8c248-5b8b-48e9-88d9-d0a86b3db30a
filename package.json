{"name": "cravin-concierge", "version": "1.0.0", "description": "TypeScript Express.js MVC Backend Application", "main": "dist/server.js", "scripts": {"build": "tsc", "start": "node dist/server.js", "dev": "nodemon --exec ts-node -r tsconfig-paths/register src/server.ts", "dev:watch": "concurrently \"tsc --watch\" \"nodemon dist/server.js\"", "clean": "<PERSON><PERSON><PERSON> dist", "prebuild": "npm run clean", "postbuild": "echo \"Build completed successfully\"", "lint": "echo \"<PERSON><PERSON> not configured yet\"", "test": "echo \"Tests not configured yet\"", "type-check": "tsc --noEmit", "vector-init": "ts-node -r tsconfig-paths/register src/scripts/runVectorInit.ts", "vector-embeddings": "ts-node -r tsconfig-paths/register src/scripts/runVectorInit.ts embeddings", "vector-health": "ts-node -r tsconfig-paths/register src/scripts/runVectorInit.ts health", "test-whatsapp-ai": "ts-node -r tsconfig-paths/register src/scripts/testWhatsAppAI.ts"}, "repository": {"type": "git", "url": "git+https://github.com/Just-Cravin/Cravin-Concierge.git"}, "keywords": ["typescript", "express", "mvc", "api", "backend", "nodejs"], "author": "", "license": "ISC", "type": "commonjs", "bugs": {"url": "https://github.com/Just-Cravin/Cravin-Concierge/issues"}, "homepage": "https://github.com/Just-<PERSON>ravin/<PERSON>ravin-Concierge#readme", "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "dependencies": {"@aws-sdk/client-bedrock": "^3.844.0", "@aws-sdk/client-bedrock-runtime": "^3.844.0", "@types/uuid": "^10.0.0", "compression": "^1.8.0", "cors": "^2.8.5", "dotenv": "^17.2.0", "express": "^5.1.0", "express-rate-limit": "^7.5.1", "helmet": "^8.1.0", "morgan": "^1.10.0", "pg": "^8.16.3", "pgvector": "^0.2.1", "uuid": "^11.1.0"}, "devDependencies": {"@types/compression": "^1.8.1", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/morgan": "^1.9.10", "@types/node": "^24.0.14", "@types/pg": "^8.15.4", "concurrently": "^9.2.0", "nodemon": "^3.1.10", "rimraf": "^6.0.1", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.8.3"}}