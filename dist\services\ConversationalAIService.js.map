{"version": 3, "file": "ConversationalAIService.js", "sourceRoot": "", "sources": ["../../src/services/ConversationalAIService.ts"], "names": [], "mappings": ";;;AAAA,qDAAkD;AAClD,+DAA4D;AAkC5D,MAAa,uBAAuB;IAApC;QACU,yBAAoB,GAAqC,IAAI,GAAG,EAAE,CAAC;IA4Q7E,CAAC;IAvQC,KAAK,CAAC,YAAY,CAChB,KAAa,EACb,SAAiB,EACjB,MAAe,EACf,iBAAgD;QAEhD,IAAI,CAAC;YAEH,MAAM,OAAO,GAAG,IAAI,CAAC,kBAAkB,CAAC,SAAS,EAAE,MAAM,EAAE,iBAAiB,CAAC,CAAC;YAG9E,MAAM,WAAW,GAAG,IAAI,CAAC,uBAAuB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;YAGjE,MAAM,eAAe,GAAG,MAAM,yCAAmB,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC;YAGhF,MAAM,mBAAmB,GAAG,IAAI,CAAC,wBAAwB,CAAC,OAAO,CAAC,CAAC;YAGnE,MAAM,MAAM,GAAG,+BAAc,CAAC,kCAAkC,CAC9D,KAAK,EACL,eAAe,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,EAC5C,mBAAmB,CACpB,CAAC;YAGF,MAAM,UAAU,GAAG,MAAM,+BAAc,CAAC,gBAAgB,CACtD,MAAM,EACN,+BAAc,CAAC,eAAe,EAAE,CACjC,CAAC;YAGF,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACpC,OAAO,CAAC,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAG3C,IAAI,OAAO,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACvC,OAAO,CAAC,eAAe,GAAG,OAAO,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC5D,OAAO,CAAC,iBAAiB,GAAG,OAAO,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;YAClE,CAAC;YAED,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;YAGlD,MAAM,UAAU,GAAG,IAAI,CAAC,mBAAmB,CAAC,eAAe,EAAE,KAAK,CAAC,CAAC;YAGpE,MAAM,kBAAkB,GAAG,IAAI,CAAC,2BAA2B,CAAC,eAAe,EAAE,KAAK,CAAC,CAAC;YAEpF,OAAO;gBACL,QAAQ,EAAE,UAAU;gBACpB,uBAAuB,EAAE,eAAe;gBACxC,UAAU;gBACV,mBAAmB,EAAE,OAAO;gBAC5B,kBAAkB;aACnB,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,2CAA2C,EAAE,KAAK,CAAC,CAAC;YAClE,MAAM,IAAI,KAAK,CAAC,4BAA4B,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE,CAAC,CAAC;QAC1G,CAAC;IACH,CAAC;IAKO,kBAAkB,CACxB,SAAiB,EACjB,MAAe,EACf,iBAAgD;QAEhD,IAAI,OAAO,GAAG,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QAEvD,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,MAAM,UAAU,GAAwB;gBACtC,MAAM,EAAE,MAAM;gBACd,SAAS;gBACT,eAAe,EAAE,EAAE;gBACnB,iBAAiB,EAAE,EAAE;gBACrB,eAAe,EAAE,iBAAiB,EAAE,eAAe,IAAI,EAAE;aAC1D,CAAC;YACF,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC;YACrD,OAAO,UAAU,CAAC;QACpB,CAAC;QAGD,IAAI,iBAAiB,IAAI,iBAAiB,CAAC,eAAe,EAAE,CAAC;YAC3D,OAAO,CAAC,eAAe,GAAG;gBACxB,GAAG,OAAO,CAAC,eAAe;gBAC1B,GAAG,iBAAiB,CAAC,eAAe;aACrC,CAAC;YACF,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;QACpD,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAKO,uBAAuB,CAAC,KAAa,EAAE,OAA4B;QACzE,MAAM,UAAU,GAAG,KAAK,CAAC,WAAW,EAAE,CAAC;QAGvC,IAAI,QAAQ,GAAG,OAAO,CAAC,eAAe,EAAE,QAAQ,CAAC;QACjD,MAAM,gBAAgB,GAAG,CAAC,OAAO,EAAE,WAAW,EAAE,SAAS,EAAE,OAAO,EAAE,UAAU,EAAE,gBAAgB,EAAE,eAAe,CAAC,CAAC;QACnH,KAAK,MAAM,GAAG,IAAI,gBAAgB,EAAE,CAAC;YACnC,IAAI,UAAU,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;gBAC7B,QAAQ,GAAG,GAAG,CAAC;gBACf,MAAM;YACR,CAAC;QACH,CAAC;QAGD,IAAI,UAAU,GAAG,OAAO,CAAC,eAAe,EAAE,UAAU,CAAC;QACrD,IAAI,UAAU,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,UAAU,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,CAAC;YACvG,UAAU,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;QAC5B,CAAC;aAAM,IAAI,UAAU,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,UAAU,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;YAChH,UAAU,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;QAC5B,CAAC;aAAM,IAAI,UAAU,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,UAAU,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;YAC/E,UAAU,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;QACrC,CAAC;QAGD,MAAM,WAAW,GAAa,EAAE,CAAC;QACjC,IAAI,UAAU,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,UAAU,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,UAAU,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;YAClI,WAAW,CAAC,IAAI,CAAC,YAAY,EAAE,QAAQ,EAAE,WAAW,CAAC,CAAC;QACxD,CAAC;QACD,IAAI,UAAU,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,UAAU,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;YAC9F,WAAW,CAAC,IAAI,CAAC,MAAM,EAAE,aAAa,CAAC,CAAC;QAC1C,CAAC;QACD,IAAI,UAAU,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,UAAU,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,UAAU,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;YACpG,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAC3B,CAAC;QACD,IAAI,UAAU,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,UAAU,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,UAAU,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;YACjI,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAC/B,CAAC;QAGD,MAAM,IAAI,GAAa,EAAE,CAAC;QAC1B,MAAM,WAAW,GAAG,CAAC,OAAO,EAAE,OAAO,EAAE,YAAY,EAAE,SAAS,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,EAAE,aAAa,CAAC,CAAC;QAC3H,KAAK,MAAM,GAAG,IAAI,WAAW,EAAE,CAAC;YAC9B,IAAI,UAAU,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;gBAC7B,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACjB,CAAC;QACH,CAAC;QAED,OAAO;YACL,KAAK;YACL,YAAY,EAAE,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,SAAS;YAC9D,QAAQ;YACR,WAAW,EAAE,UAAU;YACvB,IAAI,EAAE,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS;YACxC,KAAK,EAAE,CAAC;YACR,oBAAoB,EAAE,GAAG;SAC1B,CAAC;IACJ,CAAC;IAKO,wBAAwB,CAAC,OAA4B;QAC3D,IAAI,OAAO,CAAC,eAAe,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACzC,OAAO,EAAE,CAAC;QACZ,CAAC;QAED,MAAM,OAAO,GAAa,EAAE,CAAC;QAC7B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,eAAe,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACxD,OAAO,CAAC,IAAI,CAAC,SAAS,OAAO,CAAC,eAAe,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;YACpD,IAAI,OAAO,CAAC,iBAAiB,CAAC,CAAC,CAAC,EAAE,CAAC;gBACjC,OAAO,CAAC,IAAI,CAAC,cAAc,OAAO,CAAC,iBAAiB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;YAC7D,CAAC;QACH,CAAC;QAED,OAAO,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC5B,CAAC;IAKO,mBAAmB,CAAC,OAA6B,EAAE,KAAa;QACtE,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACzB,OAAO,GAAG,CAAC;QACb,CAAC;QAGD,MAAM,aAAa,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,MAAM,EAAE,EAAE,CAAC,GAAG,GAAG,MAAM,CAAC,gBAAgB,EAAE,CAAC,CAAC,GAAG,OAAO,CAAC,MAAM,CAAC;QAGzG,MAAM,UAAU,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC;QAC3C,MAAM,gBAAgB,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,GAAG,EAAE,EAAE,GAAG,CAAC,CAAC;QAGxD,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC;QAEvD,OAAO,IAAI,CAAC,GAAG,CAAC,aAAa,GAAG,gBAAgB,GAAG,YAAY,EAAE,GAAG,CAAC,CAAC;IACxE,CAAC;IAKO,2BAA2B,CAAC,OAA6B,EAAE,MAAc;QAC/E,MAAM,WAAW,GAAa,EAAE,CAAC;QAEjC,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACvB,MAAM,WAAW,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;YAElE,IAAI,WAAW,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,WAAW,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;gBACzE,WAAW,CAAC,IAAI,CAAC,+BAA+B,CAAC,CAAC;gBAClD,WAAW,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;gBAC5C,WAAW,CAAC,IAAI,CAAC,kCAAkC,CAAC,CAAC;YACvD,CAAC;YAED,IAAI,WAAW,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;gBACjC,WAAW,CAAC,IAAI,CAAC,oCAAoC,CAAC,CAAC;gBACvD,WAAW,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;YAC9C,CAAC;YAED,IAAI,WAAW,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;gBACrC,WAAW,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;gBAC1C,WAAW,CAAC,IAAI,CAAC,+BAA+B,CAAC,CAAC;YACpD,CAAC;YAGD,WAAW,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAC;YAChD,WAAW,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;QACjD,CAAC;aAAM,CAAC;YACN,WAAW,CAAC,IAAI,CAAC,gDAAgD,CAAC,CAAC;YACnE,WAAW,CAAC,IAAI,CAAC,2CAA2C,CAAC,CAAC;YAC9D,WAAW,CAAC,IAAI,CAAC,uCAAuC,CAAC,CAAC;QAC5D,CAAC;QAED,OAAO,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACjC,CAAC;IAKD,YAAY,CAAC,SAAiB;QAC5B,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;IAC9C,CAAC;IAKD,qBAAqB,CACnB,SAAiB,EACjB,WAA4D;QAE5D,MAAM,OAAO,GAAG,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QACzD,IAAI,OAAO,EAAE,CAAC;YACZ,OAAO,CAAC,eAAe,GAAG,EAAE,GAAG,OAAO,CAAC,eAAe,EAAE,GAAG,WAAW,EAAE,CAAC;YACzE,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;QACpD,CAAC;IACH,CAAC;IAKD,UAAU,CAAC,SAAiB;QAC1B,OAAO,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;IAClD,CAAC;CACF;AA7QD,0DA6QC;AAGY,QAAA,uBAAuB,GAAG,IAAI,uBAAuB,EAAE,CAAC"}