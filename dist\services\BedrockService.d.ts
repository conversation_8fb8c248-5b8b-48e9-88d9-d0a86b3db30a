export declare class BedrockService {
    private client;
    constructor();
    generateResponse(prompt: string, systemPrompt?: string): Promise<string>;
    generateEmbedding(text: string): Promise<number[]>;
    generateEmbeddings(texts: string[]): Promise<number[][]>;
    createBusinessRecommendationPrompt(userQuery: string, businessData: any[], conversationContext?: string): string;
    getSystemPrompt(): string;
}
export declare const bedrockService: BedrockService;
//# sourceMappingURL=BedrockService.d.ts.map