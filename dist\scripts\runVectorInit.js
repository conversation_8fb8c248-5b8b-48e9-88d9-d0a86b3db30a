#!/usr/bin/env ts-node
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const initializeVectorDatabase_1 = require("./initializeVectorDatabase");
const database_1 = require("../config/database");
async function main() {
    const args = process.argv.slice(2);
    const command = args[0] || 'init';
    console.log('🤖 Cravin Concierge Vector Database Setup');
    console.log('==========================================');
    try {
        await (0, database_1.initDatabase)();
        switch (command) {
            case 'init':
                console.log('🚀 Running full vector database initialization...');
                await initializeVectorDatabase_1.VectorDatabaseInitializer.initialize();
                break;
            case 'embeddings':
                console.log('🧠 Generating embeddings for existing data...');
                await initializeVectorDatabase_1.VectorDatabaseInitializer.generateEmbeddingsForExistingData();
                break;
            case 'health':
                console.log('🏥 Checking vector database health...');
                await initializeVectorDatabase_1.VectorDatabaseInitializer.checkHealth();
                break;
            case 'help':
            default:
                console.log('Available commands:');
                console.log('  init       - Initialize vector database (default)');
                console.log('  embeddings - Generate embeddings for existing data');
                console.log('  health     - Check vector database health');
                console.log('  help       - Show this help message');
                console.log('');
                console.log('Usage: npm run vector-init [command]');
                console.log('   or: ts-node src/scripts/runVectorInit.ts [command]');
                break;
        }
        console.log('');
        console.log('✅ Script completed successfully!');
        process.exit(0);
    }
    catch (error) {
        console.error('');
        console.error('❌ Script failed:', error);
        process.exit(1);
    }
}
if (require.main === module) {
    main();
}
//# sourceMappingURL=runVectorInit.js.map