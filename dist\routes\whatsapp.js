"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.whatsAppRoutes = exports.WhatsAppRoutes = void 0;
const express_1 = require("express");
const WhatsAppController_1 = require("@controllers/WhatsAppController");
class WhatsAppRoutes {
    constructor() {
        this.router = (0, express_1.Router)();
        this.whatsAppController = new WhatsAppController_1.WhatsAppController();
        this.initializeRoutes();
    }
    initializeRoutes() {
        this.router.get('/webhook', this.whatsAppController.verifyWebhook);
        this.router.post('/webhook', this.whatsAppController.handleWebhook);
    }
}
exports.WhatsAppRoutes = WhatsAppRoutes;
exports.whatsAppRoutes = new WhatsAppRoutes().router;
//# sourceMappingURL=whatsapp.js.map