import { VectorSearchResult, BusinessSearchQuery } from '../models/Business';
export declare class VectorSearchService {
    searchBusinesses(searchQuery: BusinessSearchQuery): Promise<VectorSearchResult[]>;
    private searchRestaurants;
    private searchRestaurantBranches;
    private searchShops;
    private searchShopBranches;
    private searchClubs;
    private searchFoodItems;
    private searchFacilities;
    createBusinessEmbedding(entity: any, entityType: string): Promise<number[]>;
}
export declare const vectorSearchService: VectorSearchService;
//# sourceMappingURL=VectorSearchService.d.ts.map