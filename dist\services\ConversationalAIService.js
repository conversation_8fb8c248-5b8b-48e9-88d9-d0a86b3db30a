"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.conversationalAIService = exports.ConversationalAIService = void 0;
const BedrockService_1 = require("./BedrockService");
const VectorSearchService_1 = require("./VectorSearchService");
class ConversationalAIService {
    constructor() {
        this.conversationContexts = new Map();
    }
    async processQuery(query, sessionId, userId, additionalContext) {
        try {
            const context = this.getOrCreateContext(sessionId, userId, additionalContext);
            const searchQuery = this.extractSearchParameters(query, context);
            const businessResults = await VectorSearchService_1.vectorSearchService.searchBusinesses(searchQuery);
            const conversationHistory = this.buildConversationHistory(context);
            const prompt = BedrockService_1.bedrockService.createBusinessRecommendationPrompt(query, businessResults.map(result => result.entity), conversationHistory);
            const aiResponse = await BedrockService_1.bedrockService.generateResponse(prompt, BedrockService_1.bedrockService.getSystemPrompt());
            context.previousQueries.push(query);
            context.previousResponses.push(aiResponse);
            if (context.previousQueries.length > 5) {
                context.previousQueries = context.previousQueries.slice(-5);
                context.previousResponses = context.previousResponses.slice(-5);
            }
            this.conversationContexts.set(sessionId, context);
            const confidence = this.calculateConfidence(businessResults, query);
            const suggestedFollowUps = this.generateFollowUpSuggestions(businessResults, query);
            return {
                response: aiResponse,
                businessRecommendations: businessResults,
                confidence,
                conversationContext: context,
                suggestedFollowUps
            };
        }
        catch (error) {
            console.error('Error processing conversational AI query:', error);
            throw new Error(`Failed to process query: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    getOrCreateContext(sessionId, userId, additionalContext) {
        let context = this.conversationContexts.get(sessionId);
        if (!context) {
            const newContext = {
                userId: userId,
                sessionId,
                previousQueries: [],
                previousResponses: [],
                userPreferences: additionalContext?.userPreferences || {}
            };
            this.conversationContexts.set(sessionId, newContext);
            return newContext;
        }
        if (additionalContext && additionalContext.userPreferences) {
            context.userPreferences = {
                ...context.userPreferences,
                ...additionalContext.userPreferences
            };
            this.conversationContexts.set(sessionId, context);
        }
        return context;
    }
    extractSearchParameters(query, context) {
        const lowerQuery = query.toLowerCase();
        let location = context.userPreferences?.location;
        const locationKeywords = ['dubai', 'abu dhabi', 'sharjah', 'ajman', 'fujairah', 'ras al khaimah', 'umm al quwain'];
        for (const loc of locationKeywords) {
            if (lowerQuery.includes(loc)) {
                location = loc;
                break;
            }
        }
        let priceRange = context.userPreferences?.priceRange;
        if (lowerQuery.includes('cheap') || lowerQuery.includes('budget') || lowerQuery.includes('affordable')) {
            priceRange = { max: 100 };
        }
        else if (lowerQuery.includes('expensive') || lowerQuery.includes('luxury') || lowerQuery.includes('high-end')) {
            priceRange = { min: 200 };
        }
        else if (lowerQuery.includes('mid-range') || lowerQuery.includes('moderate')) {
            priceRange = { min: 50, max: 200 };
        }
        const entityTypes = [];
        if (lowerQuery.includes('restaurant') || lowerQuery.includes('food') || lowerQuery.includes('eat') || lowerQuery.includes('dine')) {
            entityTypes.push('restaurant', 'branch', 'food_item');
        }
        if (lowerQuery.includes('shop') || lowerQuery.includes('store') || lowerQuery.includes('buy')) {
            entityTypes.push('shop', 'shop_branch');
        }
        if (lowerQuery.includes('club') || lowerQuery.includes('nightlife') || lowerQuery.includes('party')) {
            entityTypes.push('club');
        }
        if (lowerQuery.includes('sport') || lowerQuery.includes('gym') || lowerQuery.includes('fitness') || lowerQuery.includes('court')) {
            entityTypes.push('facility');
        }
        const tags = [];
        const tagKeywords = ['halal', 'vegan', 'vegetarian', 'italian', 'indian', 'chinese', 'arabic', 'fast food', 'fine dining'];
        for (const tag of tagKeywords) {
            if (lowerQuery.includes(tag)) {
                tags.push(tag);
            }
        }
        return {
            query,
            entity_types: entityTypes.length > 0 ? entityTypes : undefined,
            location,
            price_range: priceRange,
            tags: tags.length > 0 ? tags : undefined,
            limit: 5,
            similarity_threshold: 0.6
        };
    }
    buildConversationHistory(context) {
        if (context.previousQueries.length === 0) {
            return '';
        }
        const history = [];
        for (let i = 0; i < context.previousQueries.length; i++) {
            history.push(`User: ${context.previousQueries[i]}`);
            if (context.previousResponses[i]) {
                history.push(`Assistant: ${context.previousResponses[i]}`);
            }
        }
        return history.join('\n');
    }
    calculateConfidence(results, query) {
        if (results.length === 0) {
            return 0.2;
        }
        const avgSimilarity = results.reduce((sum, result) => sum + result.similarity_score, 0) / results.length;
        const queryWords = query.split(' ').length;
        const specificityBonus = Math.min(queryWords / 10, 0.2);
        const resultsBonus = Math.min(results.length / 5, 0.2);
        return Math.min(avgSimilarity + specificityBonus + resultsBonus, 1.0);
    }
    generateFollowUpSuggestions(results, _query) {
        const suggestions = [];
        if (results.length > 0) {
            const entityTypes = [...new Set(results.map(r => r.entity_type))];
            if (entityTypes.includes('restaurant') || entityTypes.includes('branch')) {
                suggestions.push('What are the operating hours?');
                suggestions.push('Do they offer delivery?');
                suggestions.push('What\'s their specialty cuisine?');
            }
            if (entityTypes.includes('club')) {
                suggestions.push('What are the booking requirements?');
                suggestions.push('What\'s the dress code?');
            }
            if (entityTypes.includes('facility')) {
                suggestions.push('How do I book a slot?');
                suggestions.push('What are the pricing options?');
            }
            suggestions.push('Show me more options nearby');
            suggestions.push('What are the reviews like?');
        }
        else {
            suggestions.push('Try searching for a different type of business');
            suggestions.push('Can you help me find restaurants instead?');
            suggestions.push('What businesses are popular in Dubai?');
        }
        return suggestions.slice(0, 3);
    }
    clearContext(sessionId) {
        this.conversationContexts.delete(sessionId);
    }
    updateUserPreferences(sessionId, preferences) {
        const context = this.conversationContexts.get(sessionId);
        if (context) {
            context.userPreferences = { ...context.userPreferences, ...preferences };
            this.conversationContexts.set(sessionId, context);
        }
    }
    getContext(sessionId) {
        return this.conversationContexts.get(sessionId);
    }
}
exports.ConversationalAIService = ConversationalAIService;
exports.conversationalAIService = new ConversationalAIService();
//# sourceMappingURL=ConversationalAIService.js.map