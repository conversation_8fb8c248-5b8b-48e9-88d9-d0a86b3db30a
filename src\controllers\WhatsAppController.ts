import { Request, Response } from 'express';
import { config } from '@config/environment';
import { conversationalAIService } from '../services/ConversationalAIService';
import { WhatsAppMessageParser, WhatsAppMessage, WhatsAppUserProfile } from '../services/WhatsAppMessageParser';
import { WhatsAppResponseFormatter, WhatsAppResponse } from '../services/WhatsAppResponseFormatter';
import { logger, ValidationError } from '../middleware/errorHandler';

/**
 * WhatsApp Controller
 * Handles WhatsApp webhook verification and message processing
 */
export class WhatsAppController {
  /**
   * Verify WhatsApp webhook
   * GET /api/whatsapp/webhook
   */
  public verifyWebhook = (req: Request, res: Response): void => {
    try {
      const { 'hub.mode': mode, 'hub.challenge': challenge, 'hub.verify_token': token } = req.query;

      // Check if mode and token are valid
      if (mode === 'subscribe' && token === config.whatsapp.verifyToken) {
        console.log('WEBHOOK VERIFIED');
        res.status(200).send(challenge);
      } else {
        console.log('Failed verification. Make sure the verify tokens match.');
        res.status(403).end();
      }
    } catch (error) {
      console.error('Error verifying webhook:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error during webhook verification',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  };

  /**
   * Handle incoming WhatsApp messages
   * POST /api/whatsapp/webhook
   */
  public handleWebhook = async (req: Request, res: Response): Promise<void> => {
    try {
      logger.info('Received WhatsApp webhook', { body: req.body });

      // Parse the webhook payload
      const { messages, userProfiles } = WhatsAppMessageParser.parseWebhookPayload(req.body);

      // Process each message
      for (const message of messages) {
        await this.processMessage(message, userProfiles);
      }

      // Always respond with 200 to acknowledge receipt
      res.status(200).json({
        success: true,
        message: 'Webhook processed successfully',
        processedMessages: messages.length
      });

    } catch (error) {
      logger.error('Error handling WhatsApp webhook', error instanceof Error ? error : new Error(String(error)));

      // Still return 200 to prevent WhatsApp from retrying
      res.status(200).json({
        success: false,
        message: 'Webhook processing failed but acknowledged',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  };

  /**
   * Process individual WhatsApp message through AI service
   */
  private async processMessage(
    message: WhatsAppMessage,
    userProfiles: WhatsAppUserProfile[]
  ): Promise<void> {
    try {
      // Handle interactive button responses
      if (message.type === 'text' && message.text) {
        const isButtonResponse = await this.handleButtonResponse(message);
        if (isButtonResponse) {
          return; // Button response was handled
        }
      }

      // Extract user query from message
      const userQuery = WhatsAppMessageParser.extractUserQuery(message);

      if (!userQuery) {
        logger.info('No processable query found in message', { messageType: message.type });

        // Send a helpful response for unsupported message types
        await this.sendUnsupportedMessageResponse(message);
        return;
      }

      // Generate session ID from phone number
      const sessionId = WhatsAppMessageParser.generateSessionId(message.from);

      // Find user profile
      const userProfile = userProfiles.find(profile => profile.phoneNumber === message.from);

      // Extract user preferences from message
      const userPreferences = WhatsAppMessageParser.extractUserPreferences(message, userProfile);

      logger.info('Processing AI query', {
        sessionId,
        query: userQuery,
        messageType: message.type,
        userPreferences
      });

      // Check if this is a first-time user and send welcome message
      const existingContext = conversationalAIService.getContext(sessionId);
      const isNewUser = !existingContext || existingContext.previousQueries.length === 0;

      if (isNewUser) {
        await this.sendWelcomeMessage(message.from);

        // Update user preferences with profile information
        if (userProfile?.name) {
          conversationalAIService.updateUserPreferences(sessionId, {
            ...userPreferences,
            userName: userProfile.name
          });
        }
      }

      // Process query through conversational AI service
      const aiResponse = await conversationalAIService.processQuery(
        userQuery,
        sessionId,
        message.from,
        { userPreferences }
      );

      // Format and send response
      await this.sendAIResponse(message.from, aiResponse);

    } catch (error) {
      logger.error('Error processing WhatsApp message', error instanceof Error ? error : new Error(String(error)), {
        messageId: message.messageId,
        from: message.from
      });

      // Send error response to user
      await this.sendErrorResponse(message.from, 'I encountered an error processing your message. Please try again.');
    }
  }

  /**
   * Handle interactive button responses
   */
  private async handleButtonResponse(message: WhatsAppMessage): Promise<boolean> {
    if (!message.text) return false;

    const text = message.text.toLowerCase().trim();

    // Check if this looks like a button response
    const buttonPatterns = [
      /^what are the operating hours\??$/i,
      /^do they offer delivery\??$/i,
      /^what's their specialty cuisine\??$/i,
      /^what are the booking requirements\??$/i,
      /^what's the dress code\??$/i,
      /^how do i book a slot\??$/i,
      /^what are the pricing options\??$/i,
      /^show me more options nearby$/i,
      /^what are the reviews like\??$/i,
      /^try searching for a different type of business$/i,
      /^can you help me find restaurants instead\??$/i,
      /^what businesses are popular in dubai\??$/i
    ];

    const isButtonResponse = buttonPatterns.some(pattern => pattern.test(text));

    // Check if this is a business selection (from interactive list)
    if (text.startsWith('business_')) {
      await this.handleBusinessSelection(message);
      return true;
    }

    if (isButtonResponse) {
      // Process as a regular query but with context that this is a follow-up
      const sessionId = WhatsAppMessageParser.generateSessionId(message.from);

      try {
        const aiResponse = await conversationalAIService.processQuery(
          message.text,
          sessionId,
          message.from
        );

        await this.sendAIResponse(message.from, aiResponse);
        return true;
      } catch (error) {
        logger.error('Error handling button response', error instanceof Error ? error : new Error(String(error)));
        await this.sendErrorResponse(message.from, 'I encountered an error processing your request. Please try again.');
        return true;
      }
    }

    return false;
  }

  /**
   * Handle business selection from interactive list
   */
  private async handleBusinessSelection(message: WhatsAppMessage): Promise<void> {
    try {
      // Parse business ID from the selection
      // Format: "business_0_restaurant-123" -> index 0, id restaurant-123
      if (!message.text) {
        await this.sendErrorResponse(message.from, 'Invalid business selection. Please try again.');
        return;
      }

      const parts = message.text.split('_');
      if (parts.length < 3) {
        await this.sendErrorResponse(message.from, 'Invalid business selection. Please try again.');
        return;
      }

      const businessIndex = parseInt(parts[1] || '0');
      const businessId = parts.slice(2).join('_');

      // Get the session context to find the business from recent recommendations
      const sessionId = WhatsAppMessageParser.generateSessionId(message.from);
      const context = conversationalAIService.getContext(sessionId);

      if (!context || context.previousQueries.length === 0) {
        await this.sendErrorResponse(message.from, 'I couldn\'t find the business details. Please search again.');
        return;
      }

      // For now, send a message indicating business details would be shown
      // In a real implementation, you'd store the business recommendations in the context
      const response = WhatsAppResponseFormatter.formatTextMessage(
        `You selected business #${businessIndex + 1}. Here are the details:\n\n` +
        `Business ID: ${businessId}\n\n` +
        `For full business details, please ask me specific questions like:\n` +
        `• "What are their operating hours?"\n` +
        `• "Do they offer delivery?"\n` +
        `• "What's their phone number?"\n` +
        `• "Show me their location"`,
        message.from
      );

      await this.sendWhatsAppMessage(response);

    } catch (error) {
      logger.error('Error handling business selection', error instanceof Error ? error : new Error(String(error)));
      await this.sendErrorResponse(message.from, 'I encountered an error getting business details. Please try again.');
    }
  }

  /**
   * Send welcome message to new users
   */
  private async sendWelcomeMessage(phoneNumber: string): Promise<void> {
    try {
      const welcomeResponse = WhatsAppResponseFormatter.formatWelcomeMessage(phoneNumber);
      await this.sendWhatsAppMessage(welcomeResponse);
    } catch (error) {
      logger.error('Error sending welcome message', error instanceof Error ? error : new Error(String(error)));
    }
  }

  /**
   * Send AI response with business recommendations
   */
  private async sendAIResponse(phoneNumber: string, aiResponse: any): Promise<void> {
    try {
      const responses = WhatsAppResponseFormatter.formatAIResponse(
        aiResponse.response,
        aiResponse.businessRecommendations,
        phoneNumber,
        aiResponse.suggestedFollowUps
      );

      // Send each response message
      for (const response of responses) {
        await this.sendWhatsAppMessage(response);

        // Add small delay between messages to ensure proper order
        if (responses.length > 1) {
          await new Promise(resolve => setTimeout(resolve, 500));
        }
      }
    } catch (error) {
      logger.error('Error sending AI response', error instanceof Error ? error : new Error(String(error)));
      await this.sendErrorResponse(phoneNumber, 'I encountered an error sending my response. Please try again.');
    }
  }

  /**
   * Send response for unsupported message types
   */
  private async sendUnsupportedMessageResponse(message: WhatsAppMessage): Promise<void> {
    try {
      let responseText = '';

      switch (message.type) {
        case 'image':
          responseText = 'I can see you sent an image! While I can\'t analyze images yet, you can describe what you\'re looking for and I\'ll help you find relevant businesses.';
          break;
        case 'audio':
          responseText = 'I received your voice message! I can\'t process audio yet, but feel free to type your question and I\'ll be happy to help.';
          break;
        case 'video':
          responseText = 'Thanks for the video! I can\'t watch videos yet, but you can describe what you need and I\'ll help you find the right businesses.';
          break;
        case 'document':
          responseText = 'I see you sent a document. I can\'t read documents yet, but you can tell me what you\'re looking for and I\'ll assist you.';
          break;
        default:
          responseText = 'I\'m not sure how to handle that type of message yet. Try sending me a text message about what businesses you\'re looking for!';
      }

      const response = WhatsAppResponseFormatter.formatTextMessage(responseText, message.from);
      await this.sendWhatsAppMessage(response);
    } catch (error) {
      logger.error('Error sending unsupported message response', error instanceof Error ? error : new Error(String(error)));
    }
  }

  /**
   * Send error response to user
   */
  private async sendErrorResponse(phoneNumber: string, errorMessage: string): Promise<void> {
    try {
      const response = WhatsAppResponseFormatter.formatErrorMessage(errorMessage, phoneNumber);
      await this.sendWhatsAppMessage(response);
    } catch (error) {
      logger.error('Error sending error response', error instanceof Error ? error : new Error(String(error)));
    }
  }

  /**
   * Send WhatsApp message via API (placeholder - needs actual WhatsApp API implementation)
   */
  private async sendWhatsAppMessage(response: WhatsAppResponse): Promise<void> {
    try {
      // TODO: Implement actual WhatsApp Business API call here
      // This would typically involve making an HTTP request to WhatsApp's API

      logger.info('WhatsApp message to send', {
        to: response.to,
        type: response.type,
        messagePreview: response.type === 'text' ? response.text?.body?.substring(0, 100) : 'Interactive message'
      });

      // For now, just log the message that would be sent
      console.log('WhatsApp Message:', JSON.stringify(response, null, 2));

      // In a real implementation, you would make an API call like:
      // const whatsappApiUrl = `https://graph.facebook.com/v18.0/${phoneNumberId}/messages`;
      // const result = await fetch(whatsappApiUrl, {
      //   method: 'POST',
      //   headers: {
      //     'Authorization': `Bearer ${accessToken}`,
      //     'Content-Type': 'application/json'
      //   },
      //   body: JSON.stringify(response)
      // });

    } catch (error) {
      logger.error('Error sending WhatsApp message', error instanceof Error ? error : new Error(String(error)));
      throw error;
    }
  }
}
