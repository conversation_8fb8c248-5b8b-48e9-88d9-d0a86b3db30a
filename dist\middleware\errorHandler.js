"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.logger = exports.validateArray = exports.validateNumber = exports.validateString = exports.validateRequired = exports.asyncHandler = exports.notFoundHandler = exports.errorHandler = exports.RateLimitError = exports.ValidationError = exports.DatabaseError = exports.VectorSearchError = exports.BedrockError = exports.APIError = void 0;
class APIError extends Error {
    constructor(message, statusCode = 500, isOperational = true, code) {
        super(message);
        this.statusCode = statusCode;
        this.isOperational = isOperational;
        this.code = code;
        this.name = 'APIError';
        Error.captureStackTrace(this, this.constructor);
    }
}
exports.APIError = APIError;
class BedrockError extends APIError {
    constructor(message, code) {
        super(`AWS Bedrock Error: ${message}`, 503, true, code);
        this.name = 'BedrockError';
    }
}
exports.BedrockError = BedrockError;
class VectorSearchError extends APIError {
    constructor(message, code) {
        super(`Vector Search Error: ${message}`, 500, true, code);
        this.name = 'VectorSearchError';
    }
}
exports.VectorSearchError = VectorSearchError;
class DatabaseError extends APIError {
    constructor(message, code) {
        super(`Database Error: ${message}`, 500, true, code);
        this.name = 'DatabaseError';
    }
}
exports.DatabaseError = DatabaseError;
class ValidationError extends APIError {
    constructor(message, field) {
        super(`Validation Error: ${message}`, 400, true, field);
        this.name = 'ValidationError';
    }
}
exports.ValidationError = ValidationError;
class RateLimitError extends APIError {
    constructor(message = 'Too many requests') {
        super(message, 429, true, 'RATE_LIMIT_EXCEEDED');
        this.name = 'RateLimitError';
    }
}
exports.RateLimitError = RateLimitError;
const errorHandler = (error, req, res, next) => {
    console.error('Error occurred:', {
        message: error.message,
        stack: error.stack,
        path: req.path,
        method: req.method,
        body: req.body,
        query: req.query,
        params: req.params,
        timestamp: new Date().toISOString()
    });
    let statusCode = 500;
    let message = 'Internal Server Error';
    let code;
    if (error instanceof APIError) {
        statusCode = error.statusCode;
        message = error.message;
        code = error.code;
    }
    else if (error.name === 'ValidationError') {
        statusCode = 400;
        message = error.message;
        code = 'VALIDATION_ERROR';
    }
    else if (error.name === 'CastError') {
        statusCode = 400;
        message = 'Invalid ID format';
        code = 'INVALID_ID';
    }
    else if (error.name === 'MongoError' || error.name === 'MongoServerError') {
        statusCode = 500;
        message = 'Database operation failed';
        code = 'DATABASE_ERROR';
    }
    else if (error.message.includes('AWS')) {
        statusCode = 503;
        message = 'External service unavailable';
        code = 'SERVICE_UNAVAILABLE';
    }
    const errorResponse = {
        success: false,
        error: {
            message,
            code,
            statusCode,
            timestamp: new Date().toISOString(),
            path: req.path,
            method: req.method
        }
    };
    if (process.env.NODE_ENV === 'development') {
        errorResponse.error.stack = error.stack;
    }
    res.status(statusCode).json(errorResponse);
};
exports.errorHandler = errorHandler;
const notFoundHandler = (req, res) => {
    const errorResponse = {
        success: false,
        error: {
            message: `Route ${req.method} ${req.path} not found`,
            code: 'ROUTE_NOT_FOUND',
            statusCode: 404,
            timestamp: new Date().toISOString(),
            path: req.path,
            method: req.method
        }
    };
    res.status(404).json(errorResponse);
};
exports.notFoundHandler = notFoundHandler;
const asyncHandler = (fn) => {
    return (req, res, next) => {
        Promise.resolve(fn(req, res, next)).catch(next);
    };
};
exports.asyncHandler = asyncHandler;
const validateRequired = (value, fieldName) => {
    if (value === undefined || value === null || value === '') {
        throw new ValidationError(`${fieldName} is required`, fieldName);
    }
};
exports.validateRequired = validateRequired;
const validateString = (value, fieldName, minLength, maxLength) => {
    (0, exports.validateRequired)(value, fieldName);
    if (typeof value !== 'string') {
        throw new ValidationError(`${fieldName} must be a string`, fieldName);
    }
    if (minLength && value.length < minLength) {
        throw new ValidationError(`${fieldName} must be at least ${minLength} characters long`, fieldName);
    }
    if (maxLength && value.length > maxLength) {
        throw new ValidationError(`${fieldName} must be no more than ${maxLength} characters long`, fieldName);
    }
};
exports.validateString = validateString;
const validateNumber = (value, fieldName, min, max) => {
    if (value !== undefined && value !== null) {
        const numValue = Number(value);
        if (isNaN(numValue)) {
            throw new ValidationError(`${fieldName} must be a valid number`, fieldName);
        }
        if (min !== undefined && numValue < min) {
            throw new ValidationError(`${fieldName} must be at least ${min}`, fieldName);
        }
        if (max !== undefined && numValue > max) {
            throw new ValidationError(`${fieldName} must be no more than ${max}`, fieldName);
        }
    }
};
exports.validateNumber = validateNumber;
const validateArray = (value, fieldName, minLength, maxLength) => {
    if (value !== undefined && value !== null) {
        if (!Array.isArray(value)) {
            throw new ValidationError(`${fieldName} must be an array`, fieldName);
        }
        if (minLength && value.length < minLength) {
            throw new ValidationError(`${fieldName} must contain at least ${minLength} items`, fieldName);
        }
        if (maxLength && value.length > maxLength) {
            throw new ValidationError(`${fieldName} must contain no more than ${maxLength} items`, fieldName);
        }
    }
};
exports.validateArray = validateArray;
exports.logger = {
    error: (message, error, context) => {
        console.error(`[ERROR] ${message}`, {
            error: error?.message,
            stack: error?.stack,
            context,
            timestamp: new Date().toISOString()
        });
    },
    warn: (message, context) => {
        console.warn(`[WARN] ${message}`, {
            context,
            timestamp: new Date().toISOString()
        });
    },
    info: (message, context) => {
        console.log(`[INFO] ${message}`, {
            context,
            timestamp: new Date().toISOString()
        });
    },
    debug: (message, context) => {
        if (process.env.NODE_ENV === 'development') {
            console.log(`[DEBUG] ${message}`, {
                context,
                timestamp: new Date().toISOString()
            });
        }
    }
};
//# sourceMappingURL=errorHandler.js.map