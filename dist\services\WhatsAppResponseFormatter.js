"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.WhatsAppResponseFormatter = void 0;
const errorHandler_1 = require("../middleware/errorHandler");
class WhatsAppResponseFormatter {
    static formatAIResponse(aiResponse, businessRecommendations, recipientPhoneNumber, suggestedFollowUps) {
        const responses = [];
        try {
            const mainResponse = this.formatTextMessage(aiResponse, recipientPhoneNumber);
            responses.push(mainResponse);
            if (businessRecommendations.length > 0) {
                const businessMessage = this.formatBusinessRecommendations(businessRecommendations, recipientPhoneNumber);
                responses.push(businessMessage);
            }
            if (suggestedFollowUps && suggestedFollowUps.length > 0) {
                const followUpMessage = this.formatFollowUpSuggestions(suggestedFollowUps, recipientPhoneNumber);
                responses.push(followUpMessage);
            }
            errorHandler_1.logger.info('Formatted WhatsApp responses', {
                recipientPhoneNumber,
                responseCount: responses.length,
                businessCount: businessRecommendations.length
            });
            return responses;
        }
        catch (error) {
            errorHandler_1.logger.error('Error formatting WhatsApp response', error instanceof Error ? error : new Error(String(error)));
            return [this.formatTextMessage('I apologize, but I encountered an error while processing your request. Please try again.', recipientPhoneNumber)];
        }
    }
    static formatTextMessage(text, recipientPhoneNumber) {
        return {
            messaging_product: 'whatsapp',
            to: recipientPhoneNumber,
            type: 'text',
            text: {
                body: this.truncateText(text, 4096),
                preview_url: true
            }
        };
    }
    static formatBusinessRecommendations(recommendations, recipientPhoneNumber) {
        const topRecommendations = recommendations.slice(0, 10);
        const sections = [{
                title: 'Recommended Businesses',
                rows: topRecommendations.map((result, index) => {
                    const entity = result.entity;
                    const title = this.truncateText(entity.name, 24);
                    let description = '';
                    switch (result.entity_type) {
                        case 'restaurant':
                        case 'branch':
                            description = `${entity.branch_address || entity.club_location || 'Dubai'}`;
                            if (entity.average_spend) {
                                description += ` • AED ${entity.average_spend} avg`;
                            }
                            if (entity.phone_number) {
                                description += ` • ${entity.phone_number}`;
                            }
                            break;
                        case 'shop':
                        case 'shop_branch':
                            description = `${entity.branch_address || 'Dubai'}`;
                            if (entity.phone_number) {
                                description += ` • ${entity.phone_number}`;
                            }
                            break;
                        case 'club':
                            description = `${entity.club_location || 'Dubai'}`;
                            if (entity.average_spend) {
                                description += ` • AED ${entity.average_spend} avg`;
                            }
                            if (entity.phone_number) {
                                description += ` • ${entity.phone_number}`;
                            }
                            break;
                        case 'food_item':
                            description = `AED ${entity.item_price}`;
                            if (entity.restaurant_name) {
                                description += ` • ${entity.restaurant_name}`;
                            }
                            break;
                        case 'facility':
                            description = `${entity.facility_type} • ${entity.club_name}`;
                            break;
                        default:
                            description = entity.description || 'Business in Dubai';
                    }
                    return {
                        id: `business_${index}_${entity.id}`,
                        title,
                        description: this.truncateText(description, 72)
                    };
                })
            }];
        return {
            messaging_product: 'whatsapp',
            to: recipientPhoneNumber,
            type: 'interactive',
            interactive: {
                type: 'list',
                header: {
                    type: 'text',
                    text: '🏢 Business Recommendations'
                },
                body: {
                    text: 'Here are some businesses that match your query. Tap on any business to get more details.'
                },
                footer: {
                    text: 'Powered by Cravin Concierge'
                },
                action: {
                    sections
                }
            }
        };
    }
    static formatFollowUpSuggestions(suggestions, recipientPhoneNumber) {
        const buttons = suggestions.slice(0, 3).map((suggestion, index) => ({
            type: 'reply',
            reply: {
                id: `followup_${index}`,
                title: this.truncateText(suggestion, 20)
            }
        }));
        return {
            messaging_product: 'whatsapp',
            to: recipientPhoneNumber,
            type: 'interactive',
            interactive: {
                type: 'button',
                body: {
                    text: 'Would you like to know more? Choose one of these options:'
                },
                action: {
                    buttons
                }
            }
        };
    }
    static formatBusinessDetails(business, recipientPhoneNumber) {
        const entity = business.entity;
        let details = `*${entity.name}*\n\n`;
        if (entity.description) {
            details += `${entity.description}\n\n`;
        }
        if (entity.phone_number) {
            details += `📞 *Phone:* ${entity.phone_number}\n`;
        }
        if (entity.branch_address) {
            details += `📍 *Address:* ${entity.branch_address}\n`;
        }
        else if (entity.club_location) {
            details += `📍 *Location:* ${entity.club_location}\n`;
        }
        if (entity.branch_emirate || entity.club_emirate) {
            details += `🏙️ *Emirate:* ${entity.branch_emirate || entity.club_emirate}\n`;
        }
        if (entity.average_spend) {
            details += `💰 *Average Spend:* AED ${entity.average_spend}\n`;
        }
        else if (entity.item_price) {
            details += `💰 *Price:* AED ${entity.item_price}\n`;
        }
        switch (business.entity_type) {
            case 'restaurant':
                if (entity.restaurant_menu_type) {
                    details += `🍽️ *Cuisine:* ${entity.restaurant_menu_type}\n`;
                }
                if (entity.take_orders) {
                    details += `📱 *Online Orders:* Available\n`;
                }
                break;
            case 'club':
                if (entity.take_booking) {
                    details += `📅 *Bookings:* Available\n`;
                }
                break;
            case 'facility':
                if (entity.facility_type) {
                    details += `🏃 *Facility Type:* ${entity.facility_type}\n`;
                }
                if (entity.facility_category) {
                    details += `🎯 *Category:* ${entity.facility_category}\n`;
                }
                break;
        }
        const tags = entity.branch_tags || entity.club_tags;
        if (tags && tags.length > 0) {
            details += `🏷️ *Tags:* ${tags.join(', ')}\n`;
        }
        if (entity.branch_maps_url) {
            details += `\n🗺️ View on Maps: ${entity.branch_maps_url}`;
        }
        return this.formatTextMessage(details, recipientPhoneNumber);
    }
    static formatErrorMessage(error, recipientPhoneNumber) {
        const errorMessage = `❌ *Error*\n\n${error}\n\nPlease try again or contact support if the problem persists.`;
        return this.formatTextMessage(errorMessage, recipientPhoneNumber);
    }
    static formatWelcomeMessage(recipientPhoneNumber) {
        const welcomeText = `👋 *Welcome to Cravin Concierge!*

I'm your AI assistant for discovering amazing local businesses in Dubai and the UAE.

You can ask me about:
🍽️ Restaurants and food
🛍️ Shops and stores  
🎉 Clubs and nightlife
🏃 Sports facilities
📍 Businesses near you

Just send me a message like:
• "Find Italian restaurants in Dubai Marina"
• "Best coffee shops near me"
• "Gyms in Jumeirah"

How can I help you today?`;
        return this.formatTextMessage(welcomeText, recipientPhoneNumber);
    }
    static truncateText(text, maxLength) {
        if (text.length <= maxLength) {
            return text;
        }
        return text.substring(0, maxLength - 3) + '...';
    }
    static cleanPhoneNumber(phoneNumber) {
        const cleaned = phoneNumber.replace(/\D/g, '');
        if (!cleaned.startsWith('971') && cleaned.length === 9) {
            return '971' + cleaned;
        }
        return cleaned;
    }
}
exports.WhatsAppResponseFormatter = WhatsAppResponseFormatter;
//# sourceMappingURL=WhatsAppResponseFormatter.js.map