"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.WhatsAppController = void 0;
const environment_1 = require("@config/environment");
const ConversationalAIService_1 = require("../services/ConversationalAIService");
const WhatsAppMessageParser_1 = require("../services/WhatsAppMessageParser");
const WhatsAppResponseFormatter_1 = require("../services/WhatsAppResponseFormatter");
const errorHandler_1 = require("../middleware/errorHandler");
class WhatsAppController {
    constructor() {
        this.verifyWebhook = (req, res) => {
            try {
                const { 'hub.mode': mode, 'hub.challenge': challenge, 'hub.verify_token': token } = req.query;
                if (mode === 'subscribe' && token === environment_1.config.whatsapp.verifyToken) {
                    console.log('WEBHOOK VERIFIED');
                    res.status(200).send(challenge);
                }
                else {
                    console.log('Failed verification. Make sure the verify tokens match.');
                    res.status(403).end();
                }
            }
            catch (error) {
                console.error('Error verifying webhook:', error);
                res.status(500).json({
                    success: false,
                    message: 'Internal server error during webhook verification',
                    error: error instanceof Error ? error.message : 'Unknown error'
                });
            }
        };
        this.handleWebhook = async (req, res) => {
            try {
                errorHandler_1.logger.info('Received WhatsApp webhook', { body: req.body });
                const { messages, userProfiles } = WhatsAppMessageParser_1.WhatsAppMessageParser.parseWebhookPayload(req.body);
                for (const message of messages) {
                    await this.processMessage(message, userProfiles);
                }
                res.status(200).json({
                    success: true,
                    message: 'Webhook processed successfully',
                    processedMessages: messages.length
                });
            }
            catch (error) {
                errorHandler_1.logger.error('Error handling WhatsApp webhook', error instanceof Error ? error : new Error(String(error)));
                res.status(200).json({
                    success: false,
                    message: 'Webhook processing failed but acknowledged',
                    error: error instanceof Error ? error.message : 'Unknown error'
                });
            }
        };
    }
    async processMessage(message, userProfiles) {
        try {
            if (message.type === 'text' && message.text) {
                const isButtonResponse = await this.handleButtonResponse(message);
                if (isButtonResponse) {
                    return;
                }
            }
            const userQuery = WhatsAppMessageParser_1.WhatsAppMessageParser.extractUserQuery(message);
            if (!userQuery) {
                errorHandler_1.logger.info('No processable query found in message', { messageType: message.type });
                await this.sendUnsupportedMessageResponse(message);
                return;
            }
            const sessionId = WhatsAppMessageParser_1.WhatsAppMessageParser.generateSessionId(message.from);
            const userProfile = userProfiles.find(profile => profile.phoneNumber === message.from);
            const userPreferences = WhatsAppMessageParser_1.WhatsAppMessageParser.extractUserPreferences(message, userProfile);
            errorHandler_1.logger.info('Processing AI query', {
                sessionId,
                query: userQuery,
                messageType: message.type,
                userPreferences
            });
            const existingContext = ConversationalAIService_1.conversationalAIService.getContext(sessionId);
            const isNewUser = !existingContext || existingContext.previousQueries.length === 0;
            if (isNewUser) {
                await this.sendWelcomeMessage(message.from);
                if (userProfile?.name) {
                    ConversationalAIService_1.conversationalAIService.updateUserPreferences(sessionId, {
                        ...userPreferences,
                        userName: userProfile.name
                    });
                }
            }
            const aiResponse = await ConversationalAIService_1.conversationalAIService.processQuery(userQuery, sessionId, message.from, { userPreferences });
            await this.sendAIResponse(message.from, aiResponse);
        }
        catch (error) {
            errorHandler_1.logger.error('Error processing WhatsApp message', error instanceof Error ? error : new Error(String(error)), {
                messageId: message.messageId,
                from: message.from
            });
            await this.sendErrorResponse(message.from, 'I encountered an error processing your message. Please try again.');
        }
    }
    async handleButtonResponse(message) {
        if (!message.text)
            return false;
        const text = message.text.toLowerCase().trim();
        const buttonPatterns = [
            /^what are the operating hours\??$/i,
            /^do they offer delivery\??$/i,
            /^what's their specialty cuisine\??$/i,
            /^what are the booking requirements\??$/i,
            /^what's the dress code\??$/i,
            /^how do i book a slot\??$/i,
            /^what are the pricing options\??$/i,
            /^show me more options nearby$/i,
            /^what are the reviews like\??$/i,
            /^try searching for a different type of business$/i,
            /^can you help me find restaurants instead\??$/i,
            /^what businesses are popular in dubai\??$/i
        ];
        const isButtonResponse = buttonPatterns.some(pattern => pattern.test(text));
        if (text.startsWith('business_')) {
            await this.handleBusinessSelection(message);
            return true;
        }
        if (isButtonResponse) {
            const sessionId = WhatsAppMessageParser_1.WhatsAppMessageParser.generateSessionId(message.from);
            try {
                const aiResponse = await ConversationalAIService_1.conversationalAIService.processQuery(message.text, sessionId, message.from);
                await this.sendAIResponse(message.from, aiResponse);
                return true;
            }
            catch (error) {
                errorHandler_1.logger.error('Error handling button response', error instanceof Error ? error : new Error(String(error)));
                await this.sendErrorResponse(message.from, 'I encountered an error processing your request. Please try again.');
                return true;
            }
        }
        return false;
    }
    async handleBusinessSelection(message) {
        try {
            if (!message.text) {
                await this.sendErrorResponse(message.from, 'Invalid business selection. Please try again.');
                return;
            }
            const parts = message.text.split('_');
            if (parts.length < 3) {
                await this.sendErrorResponse(message.from, 'Invalid business selection. Please try again.');
                return;
            }
            const businessIndex = parseInt(parts[1] || '0');
            const businessId = parts.slice(2).join('_');
            const sessionId = WhatsAppMessageParser_1.WhatsAppMessageParser.generateSessionId(message.from);
            const context = ConversationalAIService_1.conversationalAIService.getContext(sessionId);
            if (!context || context.previousQueries.length === 0) {
                await this.sendErrorResponse(message.from, 'I couldn\'t find the business details. Please search again.');
                return;
            }
            const response = WhatsAppResponseFormatter_1.WhatsAppResponseFormatter.formatTextMessage(`You selected business #${businessIndex + 1}. Here are the details:\n\n` +
                `Business ID: ${businessId}\n\n` +
                `For full business details, please ask me specific questions like:\n` +
                `• "What are their operating hours?"\n` +
                `• "Do they offer delivery?"\n` +
                `• "What's their phone number?"\n` +
                `• "Show me their location"`, message.from);
            await this.sendWhatsAppMessage(response);
        }
        catch (error) {
            errorHandler_1.logger.error('Error handling business selection', error instanceof Error ? error : new Error(String(error)));
            await this.sendErrorResponse(message.from, 'I encountered an error getting business details. Please try again.');
        }
    }
    async sendWelcomeMessage(phoneNumber) {
        try {
            const welcomeResponse = WhatsAppResponseFormatter_1.WhatsAppResponseFormatter.formatWelcomeMessage(phoneNumber);
            await this.sendWhatsAppMessage(welcomeResponse);
        }
        catch (error) {
            errorHandler_1.logger.error('Error sending welcome message', error instanceof Error ? error : new Error(String(error)));
        }
    }
    async sendAIResponse(phoneNumber, aiResponse) {
        try {
            const responses = WhatsAppResponseFormatter_1.WhatsAppResponseFormatter.formatAIResponse(aiResponse.response, aiResponse.businessRecommendations, phoneNumber, aiResponse.suggestedFollowUps);
            for (const response of responses) {
                await this.sendWhatsAppMessage(response);
                if (responses.length > 1) {
                    await new Promise(resolve => setTimeout(resolve, 500));
                }
            }
        }
        catch (error) {
            errorHandler_1.logger.error('Error sending AI response', error instanceof Error ? error : new Error(String(error)));
            await this.sendErrorResponse(phoneNumber, 'I encountered an error sending my response. Please try again.');
        }
    }
    async sendUnsupportedMessageResponse(message) {
        try {
            let responseText = '';
            switch (message.type) {
                case 'image':
                    responseText = 'I can see you sent an image! While I can\'t analyze images yet, you can describe what you\'re looking for and I\'ll help you find relevant businesses.';
                    break;
                case 'audio':
                    responseText = 'I received your voice message! I can\'t process audio yet, but feel free to type your question and I\'ll be happy to help.';
                    break;
                case 'video':
                    responseText = 'Thanks for the video! I can\'t watch videos yet, but you can describe what you need and I\'ll help you find the right businesses.';
                    break;
                case 'document':
                    responseText = 'I see you sent a document. I can\'t read documents yet, but you can tell me what you\'re looking for and I\'ll assist you.';
                    break;
                default:
                    responseText = 'I\'m not sure how to handle that type of message yet. Try sending me a text message about what businesses you\'re looking for!';
            }
            const response = WhatsAppResponseFormatter_1.WhatsAppResponseFormatter.formatTextMessage(responseText, message.from);
            await this.sendWhatsAppMessage(response);
        }
        catch (error) {
            errorHandler_1.logger.error('Error sending unsupported message response', error instanceof Error ? error : new Error(String(error)));
        }
    }
    async sendErrorResponse(phoneNumber, errorMessage) {
        try {
            const response = WhatsAppResponseFormatter_1.WhatsAppResponseFormatter.formatErrorMessage(errorMessage, phoneNumber);
            await this.sendWhatsAppMessage(response);
        }
        catch (error) {
            errorHandler_1.logger.error('Error sending error response', error instanceof Error ? error : new Error(String(error)));
        }
    }
    async sendWhatsAppMessage(response) {
        try {
            errorHandler_1.logger.info('WhatsApp message to send', {
                to: response.to,
                type: response.type,
                messagePreview: response.type === 'text' ? response.text?.body?.substring(0, 100) : 'Interactive message'
            });
            console.log('WhatsApp Message:', JSON.stringify(response, null, 2));
        }
        catch (error) {
            errorHandler_1.logger.error('Error sending WhatsApp message', error instanceof Error ? error : new Error(String(error)));
            throw error;
        }
    }
}
exports.WhatsAppController = WhatsAppController;
//# sourceMappingURL=WhatsAppController.js.map