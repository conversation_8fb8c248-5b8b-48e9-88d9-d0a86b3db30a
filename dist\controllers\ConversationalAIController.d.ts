import { Request, Response } from 'express';
export declare class ConversationalAIController {
    static chat(req: Request, res: Response): Promise<void>;
    static getContext(req: Request, res: Response): Promise<void>;
    static clearContext(req: Request, res: Response): Promise<void>;
    static updatePreferences(req: Request, res: Response): Promise<void>;
    static healthCheck(req: Request, res: Response): Promise<void>;
}
//# sourceMappingURL=ConversationalAIController.d.ts.map