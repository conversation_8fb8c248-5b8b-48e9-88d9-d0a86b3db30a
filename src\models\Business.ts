

/**
 * Base interface for business entities with vector embeddings
 */
export interface BaseBusinessEntity {
  id: string;
  name: string;
  description?: string;
  embedding?: number[];
  created_at: Date;
  status: boolean;
  // Common properties that may exist on different entity types
  phone_number?: string;
  branch_address?: string;
  club_location?: string;
  branch_emirate?: string;
  club_emirate?: string;
  average_spend?: number;
  branch_tags?: string[];
  club_tags?: string[];
  branch_logo?: string;
  club_logo_url?: string;
  restaurant_logo_url?: string;
  branch_maps_url?: string;
  branch_timings?: any;
  club_timings?: any;
  restaurant_menu_type?: string;
  take_orders?: boolean;
  order_link?: string;
  take_booking?: boolean;
  booking_link?: string;
  item_price?: number;
  item_type?: string;
  restaurant_name?: string;
  facility_type?: string;
  facility_category?: string;
  club_name?: string;
}

/**
 * Restaurant model with vector embeddings
 */
export interface Restaurant extends BaseBusinessEntity {
  restaurant_id: string;
  restaurant_name: string;
  restaurant_logo_url?: string;
  restaurant_bg_img_url?: string;
  restaurant_social_links?: any;
  restaurant_cancellation_number?: string;
  restaurant_timings?: any;
  phone_number: string;
  take_orders: boolean;
  order_policy?: string[];
  payment_methods?: any;
  subscription_status: boolean;
  order_link?: string;
  restaurant_menu_type?: string;
  restaurant_emails?: string[];
  customer_review_link?: string;
  is_only_for_listing: boolean;
}

/**
 * Restaurant branch model with vector embeddings
 */
export interface RestaurantBranch extends BaseBusinessEntity {
  branch_id: string;
  fk_restaurant_id: string;
  branch_name: string;
  branch_address: string;
  phone_number: string;
  branch_timings?: any;
  branch_logo?: string;
  branch_description?: string;
  branch_maps_url?: string;
  branch_location?: any;
  branch_preparation_time?: number;
  branch_payment_modes?: string[];
  payment_methods?: any;
  branch_emirate?: string;
  average_spend?: number;
  branch_tags?: string[];
  branch_display_name?: string;
  branch_timezone: string;
  delivery_module: boolean;
}

/**
 * Shop model with vector embeddings
 */
export interface Shop extends BaseBusinessEntity {
  shop_id: string;
  shop_name: string;
  shop_logo_url?: string;
  shop_bg_img_url?: string;
  shop_social_links?: any;
  shop_cancellation_number?: string;
  shop_timings?: any;
  phone_number: string;
  take_orders: boolean;
  order_policy?: string[];
  payment_methods?: any;
  subscription_status: boolean;
  order_link?: string;
  shop_emails?: any;
  generate_quotation: boolean;
  is_only_for_listing: boolean;
}

/**
 * Shop branch model with vector embeddings
 */
export interface ShopBranch extends BaseBusinessEntity {
  branch_id: string;
  fk_shop_id: string;
  branch_name: string;
  branch_address: string;
  phone_number: string;
  branch_timings?: any;
  branch_logo?: string;
  branch_description?: string;
  branch_maps_url?: string;
  branch_location?: any;
  branch_payment_modes?: string[];
  payment_methods?: any;
  branch_emirate?: string;
  average_spend?: number;
  branch_tags?: string[];
  branch_display_name?: string;
  branch_timezone: string;
}

/**
 * Club model with vector embeddings
 */
export interface Club extends BaseBusinessEntity {
  club_id: string;
  club_name: string;
  take_booking: boolean;
  booking_link?: string;
  phone_number: string;
  club_logo_url?: string;
  club_bg_img_url?: string;
  club_location?: string;
  club_location_url?: string;
  booking_policy?: string[];
  club_social_links?: any;
  club_cancellation_number?: string;
  club_timings?: any;
  club_emails?: any;
  payment_method?: string;
  subscription_status: boolean;
  enable_support: boolean;
  club_emirate?: string;
  average_spend?: number;
  club_tags?: string[];
  is_only_for_listing: boolean;
}

/**
 * Food item model with vector embeddings
 */
export interface FoodItem extends BaseBusinessEntity {
  item_id: string;
  fk_branch_id?: string;
  item_name: string;
  item_description?: string;
  item_type: string;
  fk_category_id?: string;
  item_price: number;
  item_image_link?: string;
  item_status: string;
  item_variants?: any;
  item_add_ons_group?: any;
  item_combos?: any;
}

/**
 * Shop item model with vector embeddings
 */
export interface ShopItem extends BaseBusinessEntity {
  item_id: string;
  fk_branch_id?: string;
  item_name: string;
  item_description?: string;
  item_type: string;
  fk_category_id?: string;
  item_price: number;
  item_image_links?: string[];
  item_status: string;
  item_variants?: any;
  item_add_ons_group?: any;
  item_quantity: number;
  item_combos?: any;
}

/**
 * Category model with vector embeddings
 */
export interface Category extends BaseBusinessEntity {
  category_id: string;
  category_name: string;
  category_description?: string;
  category_availability: string;
  category_availability_timings?: any;
  fk_branch_id?: string;
}

/**
 * Facility model with vector embeddings
 */
export interface Facility extends BaseBusinessEntity {
  facility_id: string;
  fk_club_id: string;
  facility_name: string;
  facility_category: string;
  facility_type: string;
  time_slot_duration: string;
  court_types: string[];
  pricing?: any;
  facility_timing?: any;
  pricing_option?: string;
  break_timing?: any;
  partial_payment_status: boolean;
  banner_images?: string[];
  pay_at_venue_status: boolean;
  facility_timezone: string;
  facility_min_duration: number;
  facility_group_id?: string;
  facility_banner_link?: string;
}

/**
 * Vector search result interface
 */
export interface VectorSearchResult {
  entity: BaseBusinessEntity;
  similarity_score: number;
  entity_type: 'restaurant' | 'shop' | 'club' | 'food_item' | 'shop_item' | 'category' | 'facility' | 'branch' | 'shop_branch';
}

/**
 * Business search query interface
 */
export interface BusinessSearchQuery {
  query: string;
  entity_types?: string[] | undefined;
  location?: string | undefined;
  price_range?: {
    min?: number;
    max?: number;
  } | undefined;
  tags?: string[] | undefined;
  limit?: number;
  similarity_threshold?: number;
}
