"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.vectorSearchService = exports.VectorSearchService = void 0;
const database_1 = require("../config/database");
const BedrockService_1 = require("./BedrockService");
const environment_1 = require("../config/environment");
const errorHandler_1 = require("../middleware/errorHandler");
class VectorSearchService {
    async searchBusinesses(searchQuery) {
        try {
            const queryEmbedding = await BedrockService_1.bedrockService.generateEmbedding(searchQuery.query);
            const results = [];
            const limit = searchQuery.limit || environment_1.config.vectorSearch.maxResults;
            const threshold = searchQuery.similarity_threshold || environment_1.config.vectorSearch.similarityThreshold;
            if (!searchQuery.entity_types || searchQuery.entity_types.includes('restaurant')) {
                const restaurantResults = await this.searchRestaurants(queryEmbedding, limit, threshold, searchQuery);
                results.push(...restaurantResults);
            }
            if (!searchQuery.entity_types || searchQuery.entity_types.includes('branch')) {
                const branchResults = await this.searchRestaurantBranches(queryEmbedding, limit, threshold, searchQuery);
                results.push(...branchResults);
            }
            if (!searchQuery.entity_types || searchQuery.entity_types.includes('shop')) {
                const shopResults = await this.searchShops(queryEmbedding, limit, threshold, searchQuery);
                results.push(...shopResults);
            }
            if (!searchQuery.entity_types || searchQuery.entity_types.includes('shop_branch')) {
                const shopBranchResults = await this.searchShopBranches(queryEmbedding, limit, threshold, searchQuery);
                results.push(...shopBranchResults);
            }
            if (!searchQuery.entity_types || searchQuery.entity_types.includes('club')) {
                const clubResults = await this.searchClubs(queryEmbedding, limit, threshold, searchQuery);
                results.push(...clubResults);
            }
            if (!searchQuery.entity_types || searchQuery.entity_types.includes('food_item')) {
                const foodItemResults = await this.searchFoodItems(queryEmbedding, limit, threshold, searchQuery);
                results.push(...foodItemResults);
            }
            if (!searchQuery.entity_types || searchQuery.entity_types.includes('facility')) {
                const facilityResults = await this.searchFacilities(queryEmbedding, limit, threshold, searchQuery);
                results.push(...facilityResults);
            }
            return results
                .sort((a, b) => b.similarity_score - a.similarity_score)
                .slice(0, limit);
        }
        catch (error) {
            errorHandler_1.logger.error('Error in vector search', error instanceof Error ? error : new Error(String(error)));
            if (error instanceof errorHandler_1.VectorSearchError || error instanceof errorHandler_1.DatabaseError) {
                throw error;
            }
            throw new errorHandler_1.VectorSearchError(`Vector search failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    async searchRestaurants(queryEmbedding, limit, threshold, searchQuery) {
        let sql = `
      SELECT 
        restaurant_id as id,
        restaurant_name as name,
        restaurant_logo_url,
        restaurant_bg_img_url,
        phone_number,
        take_orders,
        order_policy,
        payment_methods,
        subscription_status,
        order_link,
        restaurant_menu_type,
        customer_review_link,
        is_only_for_listing,
        created_at,
        embedding <-> $1::vector as distance,
        1 - (embedding <-> $1::vector) as similarity_score
      FROM "Restaurants"
      WHERE embedding IS NOT NULL
        AND subscription_status = true
        AND is_only_for_listing = false
    `;
        const params = [JSON.stringify(queryEmbedding)];
        let paramIndex = 2;
        if (searchQuery.location) {
        }
        sql += ` AND (1 - (embedding <-> $1::vector)) >= $${paramIndex}`;
        params.push(threshold);
        paramIndex++;
        sql += ` ORDER BY embedding <-> $1::vector LIMIT $${paramIndex}`;
        params.push(limit);
        const result = await (0, database_1.query)(sql, params);
        return result.rows.map(row => ({
            entity: {
                id: row.id,
                name: row.name,
                description: `${row.restaurant_menu_type || 'Restaurant'} - ${row.take_orders ? 'Accepts orders' : 'Listing only'}`,
                embedding: undefined,
                created_at: row.created_at,
                status: row.subscription_status,
                ...row
            },
            similarity_score: row.similarity_score,
            entity_type: 'restaurant'
        }));
    }
    async searchRestaurantBranches(queryEmbedding, limit, threshold, searchQuery) {
        let sql = `
      SELECT 
        b.branch_id as id,
        b.branch_name as name,
        b.branch_description as description,
        b.branch_address,
        b.phone_number,
        b.branch_timings,
        b.branch_logo,
        b.branch_maps_url,
        b.branch_location,
        b.branch_emirate,
        b.average_spend,
        b.branch_tags,
        b.branch_display_name,
        b.delivery_module,
        b.status,
        b.created_at,
        r.restaurant_name,
        r.restaurant_menu_type,
        embedding <-> $1::vector as distance,
        1 - (embedding <-> $1::vector) as similarity_score
      FROM "Branches" b
      JOIN "Restaurants" r ON b.fk_restaurant_id = r.restaurant_id
      WHERE b.embedding IS NOT NULL
        AND b.status = true
        AND r.subscription_status = true
    `;
        const params = [JSON.stringify(queryEmbedding)];
        let paramIndex = 2;
        if (searchQuery.location) {
            sql += ` AND (LOWER(b.branch_emirate) LIKE LOWER($${paramIndex}) OR LOWER(b.branch_address) LIKE LOWER($${paramIndex}))`;
            params.push(`%${searchQuery.location}%`);
            paramIndex++;
        }
        if (searchQuery.price_range) {
            if (searchQuery.price_range.min) {
                sql += ` AND b.average_spend >= $${paramIndex}`;
                params.push(searchQuery.price_range.min);
                paramIndex++;
            }
            if (searchQuery.price_range.max) {
                sql += ` AND b.average_spend <= $${paramIndex}`;
                params.push(searchQuery.price_range.max);
                paramIndex++;
            }
        }
        sql += ` AND (1 - (embedding <-> $1::vector)) >= $${paramIndex}`;
        params.push(threshold);
        paramIndex++;
        sql += ` ORDER BY embedding <-> $1::vector LIMIT $${paramIndex}`;
        params.push(limit);
        const result = await (0, database_1.query)(sql, params);
        return result.rows.map(row => ({
            entity: {
                id: row.id,
                name: row.name,
                description: row.description || `${row.restaurant_name} branch in ${row.branch_emirate || 'Dubai'}`,
                embedding: undefined,
                created_at: row.created_at,
                status: row.status,
                ...row
            },
            similarity_score: row.similarity_score,
            entity_type: 'branch'
        }));
    }
    async searchShops(queryEmbedding, limit, threshold, searchQuery) {
        let sql = `
      SELECT 
        shop_id as id,
        shop_name as name,
        shop_logo_url,
        shop_bg_img_url,
        phone_number,
        take_orders,
        order_policy,
        payment_methods,
        subscription_status,
        order_link,
        generate_quotation,
        is_only_for_listing,
        created_at,
        embedding <-> $1::vector as distance,
        1 - (embedding <-> $1::vector) as similarity_score
      FROM "Shops"
      WHERE embedding IS NOT NULL
        AND subscription_status = true
        AND is_only_for_listing = false
    `;
        const params = [JSON.stringify(queryEmbedding)];
        let paramIndex = 2;
        sql += ` AND (1 - (embedding <-> $1::vector)) >= $${paramIndex}`;
        params.push(threshold);
        paramIndex++;
        sql += ` ORDER BY embedding <-> $1::vector LIMIT $${paramIndex}`;
        params.push(limit);
        const result = await (0, database_1.query)(sql, params);
        return result.rows.map(row => ({
            entity: {
                id: row.id,
                name: row.name,
                description: `Shop - ${row.take_orders ? 'Accepts orders' : 'Listing only'}${row.generate_quotation ? ', Provides quotations' : ''}`,
                embedding: undefined,
                created_at: row.created_at,
                status: row.subscription_status,
                ...row
            },
            similarity_score: row.similarity_score,
            entity_type: 'shop'
        }));
    }
    async searchShopBranches(queryEmbedding, limit, threshold, searchQuery) {
        let sql = `
      SELECT
        b.branch_id as id,
        b.branch_name as name,
        b.branch_description as description,
        b.branch_address,
        b.phone_number,
        b.branch_timings,
        b.branch_logo,
        b.branch_maps_url,
        b.branch_location,
        b.branch_emirate,
        b.average_spend,
        b.branch_tags,
        b.branch_display_name,
        b.status,
        b.created_at,
        s.shop_name,
        embedding <-> $1::vector as distance,
        1 - (embedding <-> $1::vector) as similarity_score
      FROM "ShopBranches" b
      JOIN "Shops" s ON b.fk_shop_id = s.shop_id
      WHERE b.embedding IS NOT NULL
        AND b.status = true
        AND s.subscription_status = true
    `;
        const params = [JSON.stringify(queryEmbedding)];
        let paramIndex = 2;
        if (searchQuery.location) {
            sql += ` AND (LOWER(b.branch_emirate) LIKE LOWER($${paramIndex}) OR LOWER(b.branch_address) LIKE LOWER($${paramIndex}))`;
            params.push(`%${searchQuery.location}%`);
            paramIndex++;
        }
        if (searchQuery.price_range) {
            if (searchQuery.price_range.min) {
                sql += ` AND b.average_spend >= $${paramIndex}`;
                params.push(searchQuery.price_range.min);
                paramIndex++;
            }
            if (searchQuery.price_range.max) {
                sql += ` AND b.average_spend <= $${paramIndex}`;
                params.push(searchQuery.price_range.max);
                paramIndex++;
            }
        }
        sql += ` AND (1 - (embedding <-> $1::vector)) >= $${paramIndex}`;
        params.push(threshold);
        paramIndex++;
        sql += ` ORDER BY embedding <-> $1::vector LIMIT $${paramIndex}`;
        params.push(limit);
        const result = await (0, database_1.query)(sql, params);
        return result.rows.map(row => ({
            entity: {
                id: row.id,
                name: row.name,
                description: row.description || `${row.shop_name} branch in ${row.branch_emirate || 'Dubai'}`,
                embedding: undefined,
                created_at: row.created_at,
                status: row.status,
                ...row
            },
            similarity_score: row.similarity_score,
            entity_type: 'shop_branch'
        }));
    }
    async searchClubs(queryEmbedding, limit, threshold, searchQuery) {
        let sql = `
      SELECT
        club_id as id,
        club_name as name,
        take_booking,
        booking_link,
        phone_number,
        club_logo_url,
        club_bg_img_url,
        club_location,
        club_location_url,
        booking_policy,
        club_social_links,
        club_timings,
        club_emails,
        subscription_status,
        club_emirate,
        average_spend,
        club_tags,
        is_only_for_listing,
        created_at,
        embedding <-> $1::vector as distance,
        1 - (embedding <-> $1::vector) as similarity_score
      FROM "Clubs"
      WHERE embedding IS NOT NULL
        AND subscription_status = true
        AND is_only_for_listing = false
    `;
        const params = [JSON.stringify(queryEmbedding)];
        let paramIndex = 2;
        if (searchQuery.location) {
            sql += ` AND (LOWER(club_emirate) LIKE LOWER($${paramIndex}) OR LOWER(club_location) LIKE LOWER($${paramIndex}))`;
            params.push(`%${searchQuery.location}%`);
            paramIndex++;
        }
        if (searchQuery.price_range) {
            if (searchQuery.price_range.min) {
                sql += ` AND average_spend >= $${paramIndex}`;
                params.push(searchQuery.price_range.min);
                paramIndex++;
            }
            if (searchQuery.price_range.max) {
                sql += ` AND average_spend <= $${paramIndex}`;
                params.push(searchQuery.price_range.max);
                paramIndex++;
            }
        }
        sql += ` AND (1 - (embedding <-> $1::vector)) >= $${paramIndex}`;
        params.push(threshold);
        paramIndex++;
        sql += ` ORDER BY embedding <-> $1::vector LIMIT $${paramIndex}`;
        params.push(limit);
        const result = await (0, database_1.query)(sql, params);
        return result.rows.map(row => ({
            entity: {
                id: row.id,
                name: row.name,
                description: `Club in ${row.club_emirate || 'Dubai'} - ${row.take_booking ? 'Accepts bookings' : 'No bookings'}`,
                embedding: undefined,
                created_at: row.created_at,
                status: row.subscription_status,
                ...row
            },
            similarity_score: row.similarity_score,
            entity_type: 'club'
        }));
    }
    async searchFoodItems(queryEmbedding, limit, threshold, searchQuery) {
        let sql = `
      SELECT
        i.item_id as id,
        i.item_name as name,
        i.item_description as description,
        i.item_type,
        i.item_price,
        i.item_image_link,
        i.item_status,
        i.item_variants,
        i.created_at,
        b.branch_name,
        b.branch_address,
        b.branch_emirate,
        r.restaurant_name,
        embedding <-> $1::vector as distance,
        1 - (embedding <-> $1::vector) as similarity_score
      FROM "Items" i
      LEFT JOIN "Branches" b ON i.fk_branch_id = b.branch_id
      LEFT JOIN "Restaurants" r ON b.fk_restaurant_id = r.restaurant_id
      WHERE i.embedding IS NOT NULL
        AND i.item_status = 'available'
        AND (b.status = true OR b.status IS NULL)
        AND (r.subscription_status = true OR r.subscription_status IS NULL)
    `;
        const params = [JSON.stringify(queryEmbedding)];
        let paramIndex = 2;
        if (searchQuery.price_range) {
            if (searchQuery.price_range.min) {
                sql += ` AND i.item_price >= $${paramIndex}`;
                params.push(searchQuery.price_range.min);
                paramIndex++;
            }
            if (searchQuery.price_range.max) {
                sql += ` AND i.item_price <= $${paramIndex}`;
                params.push(searchQuery.price_range.max);
                paramIndex++;
            }
        }
        sql += ` AND (1 - (embedding <-> $1::vector)) >= $${paramIndex}`;
        params.push(threshold);
        paramIndex++;
        sql += ` ORDER BY embedding <-> $1::vector LIMIT $${paramIndex}`;
        params.push(limit);
        const result = await (0, database_1.query)(sql, params);
        return result.rows.map(row => ({
            entity: {
                id: row.id,
                name: row.name,
                description: row.description || `${row.item_type} item from ${row.restaurant_name || 'restaurant'}`,
                embedding: undefined,
                created_at: row.created_at,
                status: row.item_status === 'available',
                ...row
            },
            similarity_score: row.similarity_score,
            entity_type: 'food_item'
        }));
    }
    async searchFacilities(queryEmbedding, limit, threshold, searchQuery) {
        let sql = `
      SELECT
        f.facility_id as id,
        f.facility_name as name,
        f.facility_category,
        f.facility_type,
        f.time_slot_duration,
        f.court_types,
        f.pricing,
        f.facility_timing,
        f.pricing_option,
        f.partial_payment_status,
        f.pay_at_venue_status,
        f.facility_timezone,
        f.facility_min_duration,
        f.status,
        f.created_at,
        c.club_name,
        c.club_location,
        c.club_emirate,
        c.average_spend,
        embedding <-> $1::vector as distance,
        1 - (embedding <-> $1::vector) as similarity_score
      FROM "Facilities" f
      JOIN "Clubs" c ON f.fk_club_id = c.club_id
      WHERE f.embedding IS NOT NULL
        AND f.status = true
        AND c.subscription_status = true
    `;
        const params = [JSON.stringify(queryEmbedding)];
        let paramIndex = 2;
        if (searchQuery.location) {
            sql += ` AND (LOWER(c.club_emirate) LIKE LOWER($${paramIndex}) OR LOWER(c.club_location) LIKE LOWER($${paramIndex}))`;
            params.push(`%${searchQuery.location}%`);
            paramIndex++;
        }
        sql += ` AND (1 - (embedding <-> $1::vector)) >= $${paramIndex}`;
        params.push(threshold);
        paramIndex++;
        sql += ` ORDER BY embedding <-> $1::vector LIMIT $${paramIndex}`;
        params.push(limit);
        const result = await (0, database_1.query)(sql, params);
        return result.rows.map(row => ({
            entity: {
                id: row.id,
                name: row.name,
                description: `${row.facility_type} facility at ${row.club_name} - ${row.facility_category}`,
                embedding: undefined,
                created_at: row.created_at,
                status: row.status,
                ...row
            },
            similarity_score: row.similarity_score,
            entity_type: 'facility'
        }));
    }
    async createBusinessEmbedding(entity, entityType) {
        let textToEmbed = '';
        switch (entityType) {
            case 'restaurant':
                textToEmbed = `${entity.restaurant_name} ${entity.restaurant_menu_type || ''} restaurant in Dubai. Phone: ${entity.phone_number}. ${entity.take_orders ? 'Accepts orders' : 'Listing only'}.`;
                break;
            case 'branch':
                textToEmbed = `${entity.branch_name} branch located at ${entity.branch_address} in ${entity.branch_emirate || 'Dubai'}. ${entity.branch_description || ''} Phone: ${entity.phone_number}. ${entity.branch_tags ? 'Tags: ' + entity.branch_tags.join(', ') : ''}`;
                break;
            case 'shop':
                textToEmbed = `${entity.shop_name} shop in Dubai. Phone: ${entity.phone_number}. ${entity.take_orders ? 'Accepts orders' : 'Listing only'}. ${entity.generate_quotation ? 'Provides quotations' : ''}`;
                break;
            case 'shop_branch':
                textToEmbed = `${entity.branch_name} shop branch located at ${entity.branch_address} in ${entity.branch_emirate || 'Dubai'}. ${entity.branch_description || ''} Phone: ${entity.phone_number}. ${entity.branch_tags ? 'Tags: ' + entity.branch_tags.join(', ') : ''}`;
                break;
            case 'club':
                textToEmbed = `${entity.club_name} club in ${entity.club_emirate || 'Dubai'}. Located at ${entity.club_location || ''}. Phone: ${entity.phone_number}. ${entity.take_booking ? 'Accepts bookings' : 'No bookings'}. ${entity.club_tags ? 'Tags: ' + entity.club_tags.join(', ') : ''}`;
                break;
            case 'food_item':
                textToEmbed = `${entity.item_name} - ${entity.item_description || ''} ${entity.item_type} food item. Price: AED ${entity.item_price}`;
                break;
            case 'facility':
                textToEmbed = `${entity.facility_name} ${entity.facility_type} facility for ${entity.facility_category}. Court types: ${entity.court_types ? entity.court_types.join(', ') : ''}. Duration: ${entity.time_slot_duration}`;
                break;
            default:
                textToEmbed = entity.name || entity.description || '';
        }
        return await BedrockService_1.bedrockService.generateEmbedding(textToEmbed);
    }
}
exports.VectorSearchService = VectorSearchService;
exports.vectorSearchService = new VectorSearchService();
//# sourceMappingURL=VectorSearchService.js.map