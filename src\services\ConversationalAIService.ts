import { bedrockService } from './BedrockService';
import { vectorSearchService } from './VectorSearchService';
import { BusinessSearchQuery, VectorSearchResult } from '../models/Business';

/**
 * Interface for conversation context
 */
interface ConversationContext {
  userId?: string;
  sessionId: string;
  previousQueries: string[];
  previousResponses: string[];
  userPreferences?: {
    location?: string;
    priceRange?: { min?: number; max?: number };
    cuisineTypes?: string[];
    businessTypes?: string[];
    userName?: string;
  };
}

/**
 * Interface for AI response
 */
interface AIResponse {
  response: string;
  businessRecommendations: VectorSearchResult[];
  confidence: number;
  conversationContext: ConversationContext;
  suggestedFollowUps?: string[];
}

/**
 * Main conversational AI service that combines vector search with AI responses
 */
export class ConversationalAIService {
  private conversationContexts: Map<string, ConversationContext> = new Map();

  /**
   * Process a user query and generate an AI response with business recommendations
   */
  async processQuery(
    query: string,
    sessionId: string,
    userId?: string,
    additionalContext?: Partial<ConversationContext>
  ): Promise<AIResponse> {
    try {
      // Get or create conversation context
      const context = this.getOrCreateContext(sessionId, userId, additionalContext);
      
      // Extract search parameters from the query
      const searchQuery = this.extractSearchParameters(query, context);
      
      // Perform vector search for relevant businesses
      const businessResults = await vectorSearchService.searchBusinesses(searchQuery);
      
      // Generate conversational context from previous interactions
      const conversationHistory = this.buildConversationHistory(context);
      
      // Create AI prompt with business data and context
      const prompt = bedrockService.createBusinessRecommendationPrompt(
        query,
        businessResults.map(result => result.entity),
        conversationHistory
      );
      
      // Generate AI response
      const aiResponse = await bedrockService.generateResponse(
        prompt,
        bedrockService.getSystemPrompt()
      );
      
      // Update conversation context
      context.previousQueries.push(query);
      context.previousResponses.push(aiResponse);
      
      // Keep only last 5 interactions to manage memory
      if (context.previousQueries.length > 5) {
        context.previousQueries = context.previousQueries.slice(-5);
        context.previousResponses = context.previousResponses.slice(-5);
      }
      
      this.conversationContexts.set(sessionId, context);
      
      // Calculate confidence based on search results and query clarity
      const confidence = this.calculateConfidence(businessResults, query);
      
      // Generate suggested follow-up questions
      const suggestedFollowUps = this.generateFollowUpSuggestions(businessResults, query);
      
      return {
        response: aiResponse,
        businessRecommendations: businessResults,
        confidence,
        conversationContext: context,
        suggestedFollowUps
      };
      
    } catch (error) {
      console.error('Error processing conversational AI query:', error);
      throw new Error(`Failed to process query: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get or create conversation context
   */
  private getOrCreateContext(
    sessionId: string,
    userId?: string,
    additionalContext?: Partial<ConversationContext>
  ): ConversationContext {
    let context = this.conversationContexts.get(sessionId);

    if (!context) {
      const newContext: ConversationContext = {
        userId: userId,
        sessionId,
        previousQueries: [],
        previousResponses: [],
        userPreferences: additionalContext?.userPreferences || {}
      };
      this.conversationContexts.set(sessionId, newContext);
      return newContext;
    }

    // Update context with additional information if provided
    if (additionalContext && additionalContext.userPreferences) {
      context.userPreferences = {
        ...context.userPreferences,
        ...additionalContext.userPreferences
      };
      this.conversationContexts.set(sessionId, context);
    }

    return context;
  }

  /**
   * Extract search parameters from natural language query
   */
  private extractSearchParameters(query: string, context: ConversationContext): BusinessSearchQuery {
    const lowerQuery = query.toLowerCase();
    
    // Extract location
    let location = context.userPreferences?.location;
    const locationKeywords = ['dubai', 'abu dhabi', 'sharjah', 'ajman', 'fujairah', 'ras al khaimah', 'umm al quwain'];
    for (const loc of locationKeywords) {
      if (lowerQuery.includes(loc)) {
        location = loc;
        break;
      }
    }
    
    // Extract price range
    let priceRange = context.userPreferences?.priceRange;
    if (lowerQuery.includes('cheap') || lowerQuery.includes('budget') || lowerQuery.includes('affordable')) {
      priceRange = { max: 100 };
    } else if (lowerQuery.includes('expensive') || lowerQuery.includes('luxury') || lowerQuery.includes('high-end')) {
      priceRange = { min: 200 };
    } else if (lowerQuery.includes('mid-range') || lowerQuery.includes('moderate')) {
      priceRange = { min: 50, max: 200 };
    }
    
    // Extract entity types based on keywords
    const entityTypes: string[] = [];
    if (lowerQuery.includes('restaurant') || lowerQuery.includes('food') || lowerQuery.includes('eat') || lowerQuery.includes('dine')) {
      entityTypes.push('restaurant', 'branch', 'food_item');
    }
    if (lowerQuery.includes('shop') || lowerQuery.includes('store') || lowerQuery.includes('buy')) {
      entityTypes.push('shop', 'shop_branch');
    }
    if (lowerQuery.includes('club') || lowerQuery.includes('nightlife') || lowerQuery.includes('party')) {
      entityTypes.push('club');
    }
    if (lowerQuery.includes('sport') || lowerQuery.includes('gym') || lowerQuery.includes('fitness') || lowerQuery.includes('court')) {
      entityTypes.push('facility');
    }
    
    // Extract tags
    const tags: string[] = [];
    const tagKeywords = ['halal', 'vegan', 'vegetarian', 'italian', 'indian', 'chinese', 'arabic', 'fast food', 'fine dining'];
    for (const tag of tagKeywords) {
      if (lowerQuery.includes(tag)) {
        tags.push(tag);
      }
    }
    
    return {
      query,
      entity_types: entityTypes.length > 0 ? entityTypes : undefined,
      location,
      price_range: priceRange,
      tags: tags.length > 0 ? tags : undefined,
      limit: 5,
      similarity_threshold: 0.6
    };
  }

  /**
   * Build conversation history string
   */
  private buildConversationHistory(context: ConversationContext): string {
    if (context.previousQueries.length === 0) {
      return '';
    }
    
    const history: string[] = [];
    for (let i = 0; i < context.previousQueries.length; i++) {
      history.push(`User: ${context.previousQueries[i]}`);
      if (context.previousResponses[i]) {
        history.push(`Assistant: ${context.previousResponses[i]}`);
      }
    }
    
    return history.join('\n');
  }

  /**
   * Calculate confidence score based on search results and query
   */
  private calculateConfidence(results: VectorSearchResult[], query: string): number {
    if (results.length === 0) {
      return 0.2;
    }
    
    // Base confidence on average similarity score
    const avgSimilarity = results.reduce((sum, result) => sum + result.similarity_score, 0) / results.length;
    
    // Adjust based on query specificity
    const queryWords = query.split(' ').length;
    const specificityBonus = Math.min(queryWords / 10, 0.2);
    
    // Adjust based on number of results
    const resultsBonus = Math.min(results.length / 5, 0.2);
    
    return Math.min(avgSimilarity + specificityBonus + resultsBonus, 1.0);
  }

  /**
   * Generate suggested follow-up questions
   */
  private generateFollowUpSuggestions(results: VectorSearchResult[], _query: string): string[] {
    const suggestions: string[] = [];
    
    if (results.length > 0) {
      const entityTypes = [...new Set(results.map(r => r.entity_type))];
      
      if (entityTypes.includes('restaurant') || entityTypes.includes('branch')) {
        suggestions.push('What are the operating hours?');
        suggestions.push('Do they offer delivery?');
        suggestions.push('What\'s their specialty cuisine?');
      }
      
      if (entityTypes.includes('club')) {
        suggestions.push('What are the booking requirements?');
        suggestions.push('What\'s the dress code?');
      }
      
      if (entityTypes.includes('facility')) {
        suggestions.push('How do I book a slot?');
        suggestions.push('What are the pricing options?');
      }
      
      // Generic suggestions
      suggestions.push('Show me more options nearby');
      suggestions.push('What are the reviews like?');
    } else {
      suggestions.push('Try searching for a different type of business');
      suggestions.push('Can you help me find restaurants instead?');
      suggestions.push('What businesses are popular in Dubai?');
    }
    
    return suggestions.slice(0, 3); // Return max 3 suggestions
  }

  /**
   * Clear conversation context for a session
   */
  clearContext(sessionId: string): void {
    this.conversationContexts.delete(sessionId);
  }

  /**
   * Update user preferences
   */
  updateUserPreferences(
    sessionId: string,
    preferences: Partial<ConversationContext['userPreferences']>
  ): void {
    const context = this.conversationContexts.get(sessionId);
    if (context) {
      context.userPreferences = { ...context.userPreferences, ...preferences };
      this.conversationContexts.set(sessionId, context);
    }
  }

  /**
   * Get conversation context for a session
   */
  getContext(sessionId: string): ConversationContext | undefined {
    return this.conversationContexts.get(sessionId);
  }
}

// Export singleton instance
export const conversationalAIService = new ConversationalAIService();
