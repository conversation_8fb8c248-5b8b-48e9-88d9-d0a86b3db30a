import { VectorSearchResult } from '../models/Business';
interface ConversationContext {
    userId?: string;
    sessionId: string;
    previousQueries: string[];
    previousResponses: string[];
    userPreferences?: {
        location?: string;
        priceRange?: {
            min?: number;
            max?: number;
        };
        cuisineTypes?: string[];
        businessTypes?: string[];
        userName?: string;
    };
}
interface AIResponse {
    response: string;
    businessRecommendations: VectorSearchResult[];
    confidence: number;
    conversationContext: ConversationContext;
    suggestedFollowUps?: string[];
}
export declare class ConversationalAIService {
    private conversationContexts;
    processQuery(query: string, sessionId: string, userId?: string, additionalContext?: Partial<ConversationContext>): Promise<AIResponse>;
    private getOrCreateContext;
    private extractSearchParameters;
    private buildConversationHistory;
    private calculateConfidence;
    private generateFollowUpSuggestions;
    clearContext(sessionId: string): void;
    updateUserPreferences(sessionId: string, preferences: Partial<ConversationContext['userPreferences']>): void;
    getContext(sessionId: string): ConversationContext | undefined;
}
export declare const conversationalAIService: ConversationalAIService;
export {};
//# sourceMappingURL=ConversationalAIService.d.ts.map