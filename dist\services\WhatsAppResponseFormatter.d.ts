import { VectorSearchResult } from '../models/Business';
export interface WhatsAppResponse {
    messaging_product: 'whatsapp';
    to: string;
    type: 'text' | 'interactive';
    text?: {
        body: string;
        preview_url?: boolean;
    };
    interactive?: {
        type: 'button' | 'list';
        header?: {
            type: 'text';
            text: string;
        };
        body: {
            text: string;
        };
        footer?: {
            text: string;
        };
        action: {
            buttons?: Array<{
                type: 'reply';
                reply: {
                    id: string;
                    title: string;
                };
            }>;
            sections?: Array<{
                title: string;
                rows: Array<{
                    id: string;
                    title: string;
                    description?: string;
                }>;
            }>;
        };
    };
}
export declare class WhatsAppResponseFormatter {
    static formatAIResponse(aiResponse: string, businessRecommendations: VectorSearchResult[], recipientPhoneNumber: string, suggestedFollowUps?: string[]): WhatsAppResponse[];
    static formatTextMessage(text: string, recipientPhoneNumber: string): WhatsAppResponse;
    private static formatBusinessRecommendations;
    private static formatFollowUpSuggestions;
    static formatBusinessDetails(business: VectorSearchResult, recipientPhoneNumber: string): WhatsAppResponse;
    static formatErrorMessage(error: string, recipientPhoneNumber: string): WhatsAppResponse;
    static formatWelcomeMessage(recipientPhoneNumber: string): WhatsAppResponse;
    private static truncateText;
    static cleanPhoneNumber(phoneNumber: string): string;
}
//# sourceMappingURL=WhatsAppResponseFormatter.d.ts.map