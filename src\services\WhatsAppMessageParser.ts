import { logger, ValidationError } from '../middleware/errorHandler';

/**
 * Interface for parsed WhatsApp message
 */
export interface WhatsAppMessage {
  messageId: string;
  from: string;
  to: string;
  timestamp: string;
  type: 'text' | 'image' | 'audio' | 'video' | 'document' | 'location' | 'contacts' | 'unknown';
  text?: string;
  image?: {
    id: string;
    mime_type: string;
    sha256: string;
    caption?: string;
  };
  location?: {
    latitude: number;
    longitude: number;
    name?: string;
    address?: string;
  };
  contact?: {
    name: string;
    phone: string;
  };
  context?: {
    from: string;
    id: string;
  };
}

/**
 * Interface for WhatsApp webhook payload
 */
export interface WhatsAppWebhookPayload {
  object: string;
  entry: Array<{
    id: string;
    changes: Array<{
      value: {
        messaging_product: string;
        metadata: {
          display_phone_number: string;
          phone_number_id: string;
        };
        contacts?: Array<{
          profile: {
            name: string;
          };
          wa_id: string;
        }>;
        messages?: Array<{
          from: string;
          id: string;
          timestamp: string;
          type: string;
          text?: {
            body: string;
          };
          image?: {
            caption?: string;
            mime_type: string;
            sha256: string;
            id: string;
          };
          location?: {
            latitude: number;
            longitude: number;
            name?: string;
            address?: string;
          };
          contacts?: Array<{
            name: {
              formatted_name: string;
            };
            phones: Array<{
              phone: string;
              type: string;
            }>;
          }>;
          context?: {
            from: string;
            id: string;
          };
        }>;
        statuses?: Array<{
          id: string;
          status: string;
          timestamp: string;
          recipient_id: string;
        }>;
      };
      field: string;
    }>;
  }>;
}

/**
 * Interface for user profile information
 */
export interface WhatsAppUserProfile {
  phoneNumber: string;
  name?: string;
  displayPhoneNumber: string;
  phoneNumberId: string;
}

/**
 * WhatsApp message parser service
 */
export class WhatsAppMessageParser {
  
  /**
   * Parse WhatsApp webhook payload and extract messages
   */
  static parseWebhookPayload(payload: any): {
    messages: WhatsAppMessage[];
    userProfiles: WhatsAppUserProfile[];
  } {
    try {
      // Validate basic payload structure
      if (!payload || typeof payload !== 'object') {
        throw new ValidationError('Invalid webhook payload: payload must be an object');
      }

      if (payload.object !== 'whatsapp_business_account') {
        throw new ValidationError('Invalid webhook payload: object must be whatsapp_business_account');
      }

      if (!Array.isArray(payload.entry)) {
        throw new ValidationError('Invalid webhook payload: entry must be an array');
      }

      const messages: WhatsAppMessage[] = [];
      const userProfiles: WhatsAppUserProfile[] = [];

      // Process each entry
      for (const entry of payload.entry) {
        if (!Array.isArray(entry.changes)) {
          continue;
        }

        // Process each change
        for (const change of entry.changes) {
          if (change.field !== 'messages') {
            continue;
          }

          const value = change.value;
          if (!value || !value.metadata) {
            continue;
          }

          const metadata = value.metadata;

          // Extract user profiles from contacts
          if (value.contacts && Array.isArray(value.contacts)) {
            for (const contact of value.contacts) {
              userProfiles.push({
                phoneNumber: contact.wa_id,
                name: contact.profile?.name,
                displayPhoneNumber: metadata.display_phone_number,
                phoneNumberId: metadata.phone_number_id
              });
            }
          }

          // Extract messages
          if (value.messages && Array.isArray(value.messages)) {
            for (const message of value.messages) {
              const parsedMessage = this.parseMessage(message, metadata);
              if (parsedMessage) {
                messages.push(parsedMessage);
              }
            }
          }
        }
      }

      logger.info('Parsed WhatsApp webhook payload', {
        messageCount: messages.length,
        userProfileCount: userProfiles.length
      });

      return { messages, userProfiles };

    } catch (error) {
      logger.error('Error parsing WhatsApp webhook payload', error instanceof Error ? error : new Error(String(error)));
      throw error;
    }
  }

  /**
   * Parse individual message from webhook
   */
  private static parseMessage(message: any, metadata: any): WhatsAppMessage | null {
    try {
      if (!message.id || !message.from || !message.timestamp || !message.type) {
        logger.warn('Invalid message structure, skipping', { message });
        return null;
      }

      const baseMessage: WhatsAppMessage = {
        messageId: message.id,
        from: message.from,
        to: metadata.phone_number_id,
        timestamp: message.timestamp,
        type: this.normalizeMessageType(message.type),
        context: message.context
      };

      // Parse based on message type
      switch (message.type) {
        case 'text':
          if (message.text && message.text.body) {
            baseMessage.text = message.text.body;
          }
          break;

        case 'image':
          if (message.image) {
            baseMessage.image = {
              id: message.image.id,
              mime_type: message.image.mime_type,
              sha256: message.image.sha256,
              caption: message.image.caption
            };
          }
          break;

        case 'location':
          if (message.location) {
            baseMessage.location = {
              latitude: message.location.latitude,
              longitude: message.location.longitude,
              name: message.location.name,
              address: message.location.address
            };
          }
          break;

        case 'contacts':
          if (message.contacts && Array.isArray(message.contacts) && message.contacts.length > 0) {
            const contact = message.contacts[0];
            if (contact.name && contact.phones && contact.phones.length > 0) {
              baseMessage.contact = {
                name: contact.name.formatted_name,
                phone: contact.phones[0].phone
              };
            }
          }
          break;

        default:
          logger.info('Unsupported message type', { type: message.type });
          baseMessage.type = 'unknown';
      }

      return baseMessage;

    } catch (error) {
      logger.error('Error parsing individual message', error instanceof Error ? error : new Error(String(error)), { message });
      return null;
    }
  }

  /**
   * Normalize message type to supported types
   */
  private static normalizeMessageType(type: string): WhatsAppMessage['type'] {
    const supportedTypes: WhatsAppMessage['type'][] = [
      'text', 'image', 'audio', 'video', 'document', 'location', 'contacts'
    ];

    if (supportedTypes.includes(type as WhatsAppMessage['type'])) {
      return type as WhatsAppMessage['type'];
    }

    return 'unknown';
  }

  /**
   * Extract user query from message for AI processing
   */
  static extractUserQuery(message: WhatsAppMessage): string | null {
    switch (message.type) {
      case 'text':
        return message.text?.trim() || null;

      case 'location':
        if (message.location) {
          const { latitude, longitude, name, address } = message.location;
          return `I'm at location: ${name || address || `${latitude}, ${longitude}`}. What businesses are nearby?`;
        }
        return null;

      case 'image':
        return message.image?.caption?.trim() || 'I sent you an image. Can you help me find related businesses?';

      default:
        return null;
    }
  }

  /**
   * Generate session ID from phone number
   */
  static generateSessionId(phoneNumber: string): string {
    // Remove any non-digit characters and ensure consistent format
    const cleanNumber = phoneNumber.replace(/\D/g, '');
    return `whatsapp_${cleanNumber}`;
  }

  /**
   * Extract user preferences from message context
   */
  static extractUserPreferences(message: WhatsAppMessage, userProfile?: WhatsAppUserProfile): any {
    const preferences: any = {};

    // Extract location preference from location message
    if (message.type === 'location' && message.location) {
      preferences.location = message.location.name || message.location.address;
    }

    // Extract location from text message
    if (message.type === 'text' && message.text) {
      const text = message.text.toLowerCase();
      const locationKeywords = ['dubai', 'abu dhabi', 'sharjah', 'ajman', 'fujairah', 'ras al khaimah', 'umm al quwain'];
      
      for (const location of locationKeywords) {
        if (text.includes(location)) {
          preferences.location = location;
          break;
        }
      }
    }

    return preferences;
  }

  /**
   * Validate webhook signature (for security)
   */
  static validateWebhookSignature(payload: string, signature: string, secret: string): boolean {
    try {
      const crypto = require('crypto');
      const expectedSignature = crypto
        .createHmac('sha256', secret)
        .update(payload)
        .digest('hex');
      
      return crypto.timingSafeEqual(
        Buffer.from(signature),
        Buffer.from(`sha256=${expectedSignature}`)
      );
    } catch (error) {
      logger.error('Error validating webhook signature', error instanceof Error ? error : new Error(String(error)));
      return false;
    }
  }
}
