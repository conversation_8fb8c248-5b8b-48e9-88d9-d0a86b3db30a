export declare class VectorDatabaseInitializer {
    static initialize(): Promise<void>;
    private static enablePgVectorExtension;
    private static addEmbeddingColumns;
    private static createVectorIndexes;
    static generateEmbeddingsForExistingData(): Promise<void>;
    private static generateEmbeddingsForTable;
    private static getIdColumn;
    static checkHealth(): Promise<void>;
}
export default VectorDatabaseInitializer;
//# sourceMappingURL=initializeVectorDatabase.d.ts.map