import { Request, Response, NextFunction } from 'express';
export declare class APIError extends Error {
    statusCode: number;
    isOperational: boolean;
    code?: string;
    constructor(message: string, statusCode?: number, isOperational?: boolean, code?: string);
}
export declare class BedrockError extends APIError {
    constructor(message: string, code?: string);
}
export declare class VectorSearchError extends APIError {
    constructor(message: string, code?: string);
}
export declare class DatabaseError extends APIError {
    constructor(message: string, code?: string);
}
export declare class ValidationError extends APIError {
    constructor(message: string, field?: string);
}
export declare class RateLimitError extends APIError {
    constructor(message?: string);
}
export declare const errorHandler: (error: Error | APIError, req: Request, res: Response, next: NextFunction) => void;
export declare const notFoundHandler: (req: Request, res: Response) => void;
export declare const asyncHandler: (fn: Function) => (req: Request, res: Response, next: NextFunction) => void;
export declare const validateRequired: (value: any, fieldName: string) => void;
export declare const validateString: (value: any, fieldName: string, minLength?: number, maxLength?: number) => void;
export declare const validateNumber: (value: any, fieldName: string, min?: number, max?: number) => void;
export declare const validateArray: (value: any, fieldName: string, minLength?: number, maxLength?: number) => void;
export declare const logger: {
    error: (message: string, error?: Error, context?: any) => void;
    warn: (message: string, context?: any) => void;
    info: (message: string, context?: any) => void;
    debug: (message: string, context?: any) => void;
};
//# sourceMappingURL=errorHandler.d.ts.map