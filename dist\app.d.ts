import { Application } from 'express';
export declare class App {
    app: Application;
    port: number;
    constructor();
    initialize(): Promise<void>;
    private validateEnvironment;
    private initializeDatabase;
    private initializeMiddleware;
    private initializeRoutes;
    listen(): void;
    private setupGracefulShutdown;
    getApp(): Application;
}
//# sourceMappingURL=app.d.ts.map